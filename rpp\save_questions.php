<?php
require_once __DIR__ . '/../middleware/auth.php';
checkGuruAccess();
require_once '../config/database.php';
require_once '../models/Rpp.php';
require_once '../models/RppQuestion.php';
require_once '../models/Guru.php';

// Cek jika bukan POST request
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    $_SESSION['error'] = "Akses tidak valid.";
    header("Location: generate_questions.php");
    exit();
}

// Validasi input
if (!isset($_POST['rpp_id']) || !isset($_POST['selected_questions']) || !isset($_POST['questions'])) {
    $_SESSION['error'] = "Data tidak lengkap.";
    header("Location: generate_questions.php");
    exit();
}

$rpp_id = $_POST['rpp_id'];
$selected_questions = $_POST['selected_questions'];
$questions_data = $_POST['questions'];

// Validasi jika ada soal yang dipilih
if (empty($selected_questions)) {
    $_SESSION['error'] = "Silakan pilih minimal satu soal untuk disimpan.";
    header("Location: configure_generation.php?rpp_id=" . $rpp_id);
    exit();
}

// Dapatkan guru_id
$guru = new Guru();
$stmt = $guru->getByUserId($_SESSION['user_id']);
if ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
    $guru_id = $row['id'];
} else {
    die("Data guru tidak ditemukan");
}

// Validasi RPP
$rpp = new Rpp();
$stmt = $rpp->getOne($rpp_id);
$rpp_data = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$rpp_data || $rpp_data['guru_id'] != $guru_id) {
    $_SESSION['error'] = "RPP tidak ditemukan atau bukan milik Anda.";
    header("Location: generate_questions.php");
    exit();
}

// Mulai transaksi database
$database = new Database();
$conn = $database->getConnection();
$conn->beginTransaction();

try {
    $rppQuestion = new RppQuestion();
    $saved_count = 0;
    
    foreach ($selected_questions as $index) {
        if (!isset($questions_data[$index])) {
            continue;
        }
        
        $question_data = $questions_data[$index];
        
        // Validasi data soal
        if (empty($question_data['question_text']) || empty($question_data['question_type']) || 
            empty($question_data['difficulty_level'])) {
            continue;
        }
        
        // Set properties
        $rppQuestion->rpp_id = $rpp_id;
        $rppQuestion->question_text = $question_data['question_text'];
        $rppQuestion->question_type = $question_data['question_type'];
        $rppQuestion->difficulty_level = $question_data['difficulty_level'];
        $rppQuestion->category = $question_data['category'] ?? 'Generated';
        $rppQuestion->source_type = 'generated';
        $rppQuestion->analysis_data = null;

        // Handle options and correct answer for multiple choice
        if ($question_data['question_type'] === 'multiple_choice') {
            // Validate and clean options data
            $options = $question_data['options'] ?? null;
            if ($options !== null) {
                // If options is a string, check if it's empty or try to decode it
                if (is_string($options)) {
                    $options = trim($options);
                    if (empty($options)) {
                        // Empty string, set to null
                        $options = null;
                    } else {
                        $decoded_options = json_decode($options, true);
                        if (json_last_error() === JSON_ERROR_NONE && is_array($decoded_options)) {
                            $options = $decoded_options;
                        } else {
                            // Invalid JSON string, set to null
                            $options = null;
                        }
                    }
                }
                // If options is an array but empty, set to null
                if (is_array($options) && empty($options)) {
                    $options = null;
                }
            }

            $rppQuestion->options = $options;
            $rppQuestion->correct_answer = $question_data['correct_answer'] ?? null;
        } else {
            $rppQuestion->options = null;
            $rppQuestion->correct_answer = null;
        }
        
        if ($rppQuestion->create()) {
            $saved_count++;
        }
    }
    
    $conn->commit();
    
    if ($saved_count > 0) {
        $_SESSION['success'] = "Berhasil menyimpan $saved_count soal ke database.";
    } else {
        $_SESSION['error'] = "Tidak ada soal yang berhasil disimpan.";
    }
    
} catch (Exception $e) {
    $conn->rollBack();
    $_SESSION['error'] = "Gagal menyimpan soal: " . $e->getMessage();
}

// Redirect ke halaman daftar soal
header("Location: questions_list.php?rpp_id=" . $rpp_id);
exit();
?>
