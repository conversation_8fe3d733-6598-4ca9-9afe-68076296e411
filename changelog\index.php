<?php
include '../config/database.php';
include '../template/header.php';
?>

<div class="container">
    <h2 class="mb-4">Changelog</h2>

    <div class="card mb-4">
        <div class="card-header bg-success text-white">
            <h5 class="mb-0">Version 2.18.0 - Implementasi Modul KD dan Optimasi Sistem</h5>
            <small><?php echo date('d F Y'); ?></small>
        </div>
        <div class="card-body">
            <ul class="list-unstyled">
                <li><i class="fas fa-plus-circle text-success"></i> Penambahan modul Kompetensi Dasar (KD) lengkap dengan CRUD functionality</li>
                <li><i class="fas fa-filter text-success"></i> Sistem filter KD berdasarkan mata pelajaran, kelas, semester, dan tahun ajaran</li>
                <li><i class="fas fa-shield-alt text-success"></i> Validasi unik kode KD per mata pelajaran, kelas, semester, dan tahun ajaran</li>
                <li><i class="fas fa-link text-success"></i> Integrasi modul KD dengan sistem mata pelajaran dan periode aktif</li>
                <li><i class="fas fa-database text-success"></i> Penambahan field tema_subtema dan materi_pokok pada tabel kompetensi_dasar</li>
                <li><i class="fas fa-trash text-danger"></i> Penghapusan modul bermasalah: rekap_nilai, rekap_absensi, dan cetak_rapor</li>
                <li><i class="fas fa-tools text-success"></i> Penyediaan modul alternatif untuk fungsi yang dihapus</li>
                <li><i class="fas fa-cog text-success"></i> Pembersihan navigasi menu dan penghapusan broken links</li>
                <li><i class="fas fa-database text-success"></i> Implementasi migration scripts untuk database restore conflict fixes</li>
                <li><i class="fas fa-file-alt text-success"></i> Peningkatan dokumentasi sistem dan troubleshooting guides</li>
                <li><i class="fas fa-sync text-success"></i> Optimasi stabilitas sistem periode akademik</li>
                <li><i class="fas fa-code text-success"></i> Pembersihan kode dan penghapusan file debug yang tidak diperlukan</li>
            </ul>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">Version 2.17.1 - Peningkatan Stabilitas dan Optimasi Sistem</h5>
            <small>Desember 2024</small>
        </div>
        <div class="card-body">
            <ul class="list-unstyled">
                <li><i class="fas fa-bug text-success"></i> Perbaikan stabilitas parsing JSON pada integrasi AI Gemini</li>
                <li><i class="fas fa-shield-alt text-success"></i> Peningkatan keamanan dan validasi input pada modul RPP</li>
                <li><i class="fas fa-database text-success"></i> Optimasi query database untuk performa yang lebih baik</li>
                <li><i class="fas fa-sync text-success"></i> Perbaikan sinkronisasi data antara modul KD dan RPP</li>
                <li><i class="fas fa-code text-success"></i> Pembersihan kode dan penghapusan file debug yang tidak diperlukan</li>
                <li><i class="fas fa-cog text-success"></i> Peningkatan konfigurasi sistem untuk stabilitas jangka panjang</li>
                <li><i class="fas fa-file-alt text-success"></i> Perbaikan format export dan konsistensi dokumentasi</li>
                <li><i class="fas fa-user-shield text-success"></i> Peningkatan mapping user-guru untuk akurasi data</li>
            </ul>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">Version 2.17.0 - Peningkatan Besar Modul RPP dengan AI dan Multi-RPP</h5>
            <small>Desember 2024</small>
        </div>
        <div class="card-body">
            <ul class="list-unstyled">
                <li><i class="fas fa-robot text-success"></i> Integrasi AI Gemini untuk generate soal otomatis dengan kualitas HOTS</li>
                <li><i class="fas fa-plus-circle text-success"></i> Fitur pembuatan soal manual dengan bulk addition</li>
                <li><i class="fas fa-brain text-success"></i> Analisis tingkat kesulitan soal (Regular vs HOTS) menggunakan AI</li>
                <li><i class="fas fa-magic text-success"></i> Saran peningkatan soal otomatis dengan opsi terima/tolak</li>
                <li><i class="fas fa-layer-group text-success"></i> Generate ujian multi-RPP dengan distribusi soal proporsional</li>
                <li><i class="fas fa-clipboard-list text-success"></i> Generate kisi-kisi ujian otomatis dengan mapping kognitif</li>
                <li><i class="fas fa-filter text-success"></i> Navigasi terpisah untuk filter soal berdasarkan jenis (Pilihan Ganda/Essay)</li>
                <li><i class="fas fa-file-export text-success"></i> Export soal dan kisi-kisi ke PDF/Word dengan format standar</li>
                <li><i class="fas fa-edit text-success"></i> Perbaikan fungsi edit soal dan validasi kata kerja</li>
                <li><i class="fas fa-cog text-success"></i> Optimasi performa dan perbaikan bug pada modul RPP</li>
            </ul>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">Version 2.16.1 - Peningkatan Modul Nilai Pengganti</h5>
            <small>15 Mei 2025</small>
        </div>
        <div class="card-body">
            <ul class="list-unstyled">
                <li><i class="fas fa-calculator text-success"></i> Penambahan opsi untuk menggunakan nilai rata-rata dari semua tugas tambahan sebagai nilai pengganti</li>
                <li><i class="fas fa-exchange-alt text-success"></i> Fitur penggantian nilai tugas, UTS, UAS, dan absen dengan nilai rata-rata</li>
                <li><i class="fas fa-check-circle text-success"></i> Validasi minimal 2 tugas tambahan untuk menggunakan nilai rata-rata</li>
                <li><i class="fas fa-eye text-success"></i> Tampilan yang berbeda untuk nilai pengganti dari satu tugas dan nilai rata-rata</li>
                <li><i class="fas fa-user-shield text-success"></i> Peningkatan keamanan pada proses penggantian nilai</li>
                <li><i class="fas fa-code text-success"></i> Perbaikan bug dan optimasi kode</li>
            </ul>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">Version 2.16.0 - Implementasi Modul Tugas Tambahan</h5>
            <small>10 Mei 2025</small>
        </div>
        <div class="card-body">
            <ul class="list-unstyled">
                <li><i class="fas fa-clipboard-list text-success"></i> Penambahan modul Tugas Tambahan untuk siswa dengan nilai di bawah standar</li>
                <li><i class="fas fa-user-plus text-success"></i> Fitur pemilihan siswa berdasarkan nilai di bawah KKM</li>
                <li><i class="fas fa-user-plus text-success"></i> Fitur pemilihan siswa berdasarkan persentase kehadiran</li>
                <li><i class="fas fa-users text-success"></i> Opsi untuk memilih semua siswa di kelas</li>
                <li><i class="fas fa-user-check text-success"></i> Opsi untuk memilih siswa secara manual</li>
                <li><i class="fas fa-edit text-success"></i> Fitur input nilai tugas tambahan</li>
                <li><i class="fas fa-eye text-success"></i> Tampilan tugas tambahan pada halaman publik siswa</li>
                <li><i class="fas fa-info-circle text-success"></i> Halaman detail tugas tambahan untuk siswa</li>
            </ul>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">Version 2.15.0 - Implementasi Halaman Publik Siswa</h5>
            <small>30 April 2025</small>
        </div>
        <div class="card-body">
            <ul class="list-unstyled">
                <li><i class="fas fa-globe text-success"></i> Penambahan halaman khusus untuk siswa tanpa perlu login</li>
                <li><i class="fas fa-search text-success"></i> Fitur pencarian data siswa berdasarkan NIS</li>
                <li><i class="fas fa-calendar-check text-success"></i> Tampilan rata-rata kehadiran untuk setiap mata pelajaran</li>
                <li><i class="fas fa-tasks text-success"></i> Informasi status pengumpulan tugas (tanpa menampilkan nilai)</li>
                <li><i class="fas fa-chart-line text-success"></i> Tampilan nilai akhir untuk setiap mata pelajaran</li>
                <li><i class="fas fa-filter text-success"></i> Filter data berdasarkan semester dan tahun ajaran</li>
                <li><i class="fas fa-mobile-alt text-success"></i> Tampilan responsif untuk perangkat mobile</li>
                <li><i class="fas fa-link text-success"></i> Akses mudah dari halaman login</li>
            </ul>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">Version 2.14.0 - Implementasi Modul Cetak Rapor</h5>
            <small>15 Maret 2025</small>
        </div>
        <div class="card-body">
            <ul class="list-unstyled">
                <li><i class="fas fa-file-pdf text-success"></i> Penambahan modul cetak rapor siswa</li>
                <li><i class="fas fa-check-circle text-success"></i> Implementasi fitur keputusan kenaikan kelas</li>
                <li><i class="fas fa-list-alt text-success"></i> Tampilan absensi per mata pelajaran pada rapor</li>
                <li><i class="fas fa-clipboard-list text-success"></i> Opsi naik kelas, naik kelas bersyarat, dan tinggal kelas</li>
                <li><i class="fas fa-edit text-success"></i> Fitur edit keputusan kenaikan kelas dengan alasan</li>
                <li><i class="fas fa-file-export text-success"></i> Export rapor ke format PDF</li>
                <li><i class="fas fa-user-shield text-success"></i> Pembatasan akses cetak rapor untuk wali kelas</li>
            </ul>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">Version 2.13.0 - Implementasi Modul Tingkat dan Jurusan</h5>
            <small>10 Maret 2025</small>
        </div>
        <div class="card-body">
            <ul class="list-unstyled">
                <li><i class="fas fa-layer-group text-success"></i> Penambahan modul manajemen Tingkat untuk mengatur jenjang kelas</li>
                <li><i class="fas fa-graduation-cap text-success"></i> Penambahan modul manajemen Jurusan untuk mengatur program studi</li>
                <li><i class="fas fa-link text-success"></i> Integrasi Tingkat dan Jurusan dengan modul Kelas</li>
                <li><i class="fas fa-filter text-success"></i> Penambahan filter Tingkat dan Jurusan pada modul Jadwal Pelajaran</li>
                <li><i class="fas fa-sync text-success"></i> Penyesuaian tampilan form untuk pemilihan Tingkat dan Jurusan</li>
            </ul>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">Version 2.12.1 - Peningkatan Modul Nilai Sikap</h5>
            <small>17 Februari 2025</small>
        </div>
        <div class="card-body">
            <ul class="list-unstyled">
                <li><i class="fas fa-file-excel text-success"></i> Penambahan fitur export data nilai sikap ke Excel</li>
                <li><i class="fas fa-file-pdf text-success"></i> Penambahan fitur export data nilai sikap ke PDF</li>
                <li><i class="fas fa-table text-success"></i> Penyempurnaan tampilan tabel data</li>
            </ul>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">Version 2.12.0 - Implementasi Modul Nilai Sikap</h5>
            <small>17 Februari 2025</small>
        </div>
        <div class="card-body">
            <ul class="list-unstyled">
                <li><i class="fas fa-star text-success"></i> Penambahan modul Nilai Sikap untuk penilaian spiritual dan sosial siswa</li>
                <li><i class="fas fa-users text-success"></i> Fitur input nilai sikap secara massal per kelas</li>
                <li><i class="fas fa-check-double text-success"></i> Validasi duplikasi data nilai sikap</li>
                <li><i class="fas fa-file-alt text-success"></i> Deskripsi otomatis berdasarkan nilai (A-D)</li>
                <li><i class="fas fa-filter text-success"></i> Filter data berdasarkan kelas, semester, dan tahun ajaran</li>
                <li><i class="fas fa-user-lock text-success"></i> Pembatasan akses hanya untuk guru</li>
                <li><i class="fas fa-table text-success"></i> Tampilan data yang informatif dengan NIS dan nama siswa</li>
            </ul>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">Version 2.11.1 - Peningkatan Modul Berita</h5>
            <small>12 Februari 2025</small>
        </div>
        <div class="card-body">
            <ul class="list-unstyled">
                <li><i class="fas fa-thumbs-up text-success"></i> Penambahan fitur like dan dislike pada berita</li>
                <li><i class="fas fa-comments text-success"></i> Penambahan fitur like dan dislike pada komentar berita</li>
                <li><i class="fas fa-sync text-success"></i> Update tampilan like/dislike secara real-time tanpa refresh halaman</li>
            </ul>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">Version 2.11.0 - Implementasi Modul RPP</h5>
            <small>12 Februari 2025</small>
        </div>
        <div class="card-body">
            <ul class="list-unstyled">
                <li><i class="fas fa-file-alt text-success"></i> Penambahan modul Rencana Pelaksanaan Pembelajaran (RPP)</li>
                <li><i class="fas fa-plus text-success"></i> Fitur pembuatan RPP dengan format standar</li>
                <li><i class="fas fa-file-export text-success"></i> Export RPP ke format PDF dan Word</li>
                <li><i class="fas fa-link text-success"></i> Integrasi dengan data mata pelajaran dan kelas yang diampu guru</li>
                <li><i class="fas fa-check text-success"></i> Validasi data RPP sebelum disimpan</li>
                <li><i class="fas fa-user-lock text-success"></i> Pembatasan akses RPP hanya untuk guru yang bersangkutan</li>
            </ul>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">Version 2.10.1 - Peningkatan Modul Perpustakaan</h5>
            <small>12 Februari 2025</small>
        </div>
        <div class="card-body">
            <ul class="list-unstyled">
                <li><i class="fas fa-hashtag text-success"></i> Implementasi sistem penomoran peminjaman otomatis</li>
                <li><i class="fas fa-cog text-success"></i> Format nomor peminjaman yang dapat dikonfigurasi</li>
                <li><i class="fas fa-file-alt text-success"></i> Penambahan kolom nomor peminjaman di laporan dan export</li>
            </ul>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">Version 2.10.0 - Implementasi Modul Perpustakaan</h5>
            <small>7 Februari 2025</small>
        </div>
        <div class="card-body">
            <ul class="list-unstyled">
                <li><i class="fas fa-book text-success"></i> Penambahan modul manajemen buku perpustakaan</li>
                <li><i class="fas fa-tags text-success"></i> Fitur manajemen kategori buku</li>
                <li><i class="fas fa-book-reader text-success"></i> Sistem peminjaman dan pengembalian buku</li>
                <li><i class="fas fa-user-tie text-success"></i> Manajemen pustakawan dari data guru</li>
                <li><i class="fas fa-chart-bar text-success"></i> Laporan peminjaman dengan export Excel dan PDF</li>
                <li><i class="fas fa-cog text-success"></i> Konfigurasi perpustakaan (durasi pinjam, denda, dll)</li>
                <li><i class="fas fa-file-import text-success"></i> Fitur import data buku melalui Excel</li>
                <li><i class="fas fa-clock text-success"></i> Perhitungan keterlambatan dan denda otomatis</li>
            </ul>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">Version 2.9.1 - Riwayat Absensi untuk Guru</h5>
            <small>6 Februari 2025</small>
        </div>
        <div class="card-body">
            <ul class="list-unstyled">
                <li><i class="fas fa-history text-success"></i> Penambahan modul Riwayat Absensi di menu Aktivitas Guru</li>
                <li><i class="fas fa-calendar-alt text-success"></i> Fitur untuk mengisi absensi pada tanggal-tanggal sebelumnya</li>
                <li><i class="fas fa-check-square text-success"></i> Integrasi dengan periode aktif untuk memastikan validitas tanggal absensi</li>
                <li><i class="fas fa-clock text-success"></i> Tampilan jadwal mengajar guru dengan status absensi yang belum diisi</li>
                <li><i class="fas fa-calendar text-success"></i> Integrasi hari libur dengan modul Riwayat Absensi</li>
            </ul>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">Version 2.9.0 - Peningkatan Modul Berita</h5>
            <small>6 Februari 2025</small>
        </div>
        <div class="card-body">
            <ul class="list-unstyled">
                <li><i class="fas fa-comments text-success"></i> Implementasi sistem komentar bersarang dengan fitur balas komentar</li>
                <li><i class="fas fa-trash text-success"></i> Perbaikan fungsionalitas hapus komentar dan berita</li>
                <li><i class="fas fa-user-shield text-success"></i> Peningkatan kontrol akses untuk guru dan admin</li>
                <li><i class="fas fa-paint-brush text-success"></i> Perbaikan tampilan dan interaksi pada modul berita</li>
            </ul>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">Version 2.8.8 - Perbaikan Bug Modul Jadwal dan Siswa</h5>
            <small>5 Februari 2025</small>
        </div>
        <div class="card-body">
            <ul class="list-unstyled">
                <li><i class="fas fa-bug text-success"></i> Perbaikan tombol "Lihat Konflik" pada modul jadwal</li>
                <li><i class="fas fa-bug text-success"></i> Perbaikan tampilan data pada form edit siswa</li>
                <li><i class="fas fa-sync text-success"></i> Pembaruan inisialisasi popover untuk kompatibilitas Bootstrap 5</li>
            </ul>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">Version 2.8.7 - Modul Rekap Nilai</h5>
            <small>20 Januari 2025</small>
        </div>
        <div class="card-body">
            <ul class="list-unstyled">
                <li><i class="fas fa-file-export text-success"></i> Menambahkan modul Rekap Nilai untuk wali kelas</li>
                <li><i class="fas fa-file-excel text-success"></i> Fitur ekspor data nilai ke format Excel dan PDF</li>
                <li><i class="fas fa-chart-line text-success"></i> Menampilkan rata-rata nilai tugas, UTS, UAS, dan nilai akhir</li>
            </ul>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">Version 2.8.6 - Manajemen Kenaikan Kelas dan Kelulusan</h5>
            <small>20 Januari 2025</small>
        </div>
        <div class="card-body">
            <ul class="list-unstyled">
                <li><i class="fas fa-user-graduate text-success"></i> Penambahan fitur kenaikan kelas untuk memproses siswa ke kelas berikutnya</li>
                <li><i class="fas fa-graduation-cap text-success"></i> Implementasi fitur kelulusan untuk memindahkan siswa ke data alumni</li>
                <li><i class="fas fa-history text-success"></i> Pencatatan riwayat kelas untuk melacak perpindahan dan status siswa</li>
                <li><i class="fas fa-sync text-success"></i> Integrasi dengan periode aktif untuk menentukan tahun kelulusan</li>
            </ul>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">Version 2.8.5 - Penyempurnaan Export Rekap Absensi</h5>
            <small>17 Januari 2025</small>
        </div>
        <div class="card-body">
            <ul class="list-unstyled">
                <li><i class="fas fa-file-export text-success"></i> Perbaikan format tampilan jam pada export rekap absensi</li>
                <li><i class="fas fa-sync text-success"></i> Sinkronisasi format jam dengan modul jadwal pelajaran</li>
                <li><i class="fas fa-user text-success"></i> Penambahan NIS siswa pada hasil export</li>

            </ul>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">Version 2.8.4 - Integrasi Guru Pengampu dengan Jadwal Pelajaran</h5>
            <small>16 Januari 2025</small>
        </div>
        <div class="card-body">
            <ul class="list-unstyled">
                <li><i class="fas fa-link text-success"></i> Integrasi data guru pengampu dari mata pelajaran ke modul jadwal</li>
                <li><i class="fas fa-sync text-success"></i> Otomatisasi pemilihan guru pengampu berdasarkan mata pelajaran yang dipilih</li>
                <li><i class="fas fa-shield-alt text-success"></i> Validasi guru pengampu sesuai dengan data yang terdaftar di mata pelajaran</li>
                <li><i class="fas fa-check text-success"></i> Peningkatan konsistensi data antara modul mata pelajaran dan jadwal</li>
            </ul>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">Version 2.8.3 - Penambahan Guru Pengampu pada Mata Pelajaran</h5>
            <small>16 Januari 2025</small>
        </div>
        <div class="card-body">
            <ul class="list-unstyled">
                <li><i class="fas fa-database text-success"></i> Penambahan tabel mapel_guru untuk relasi guru pengampu dengan mata pelajaran</li>
                <li><i class="fas fa-users text-success"></i> Implementasi fitur multiple guru pengampu pada setiap mata pelajaran</li>
                <li><i class="fas fa-list text-success"></i> Penambahan kolom guru pengampu pada daftar mata pelajaran</li>
                <li><i class="fas fa-check text-success"></i> Optimasi form mata pelajaran dengan multiple select untuk guru pengampu</li>
            </ul>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">Version ******* - Penanganan Jadwal Bertabrakan</h5>
            <small>16 Januari 2025</small>
        </div>
        <div class="card-body">
            <ul class="list-unstyled">
                <li><i class="fas fa-exclamation-triangle text-warning"></i> Penambahan fitur deteksi jadwal yang bertabrakan</li>
                <li><i class="fas fa-eye text-success"></i> Penambahan indikator visual untuk jadwal yang bertabrakan</li>
                <li><i class="fas fa-check text-success"></i> Tetap memungkinkan penyimpanan jadwal yang bertabrakan dengan konfirmasi</li>
                <li><i class="fas fa-info-circle text-success"></i> Penambahan informasi detail konflik jadwal</li>
            </ul>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">Version 2.8.2.4 - Peningkatan Logika Tampilan Jadwal</h5>
            <small>16 Januari 2025</small>
        </div>
        <div class="card-body">
            <ul class="list-unstyled">
                <li><i class="fas fa-clock text-success"></i> Perbaikan logika penggabungan jam pelajaran berdasarkan kontinuitas waktu</li>
                <li><i class="fas fa-list text-success"></i> Pemisahan tampilan jam yang tidak bersambung</li>
                <li><i class="fas fa-check text-success"></i> Peningkatan akurasi informasi range waktu jadwal</li>
            </ul>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">Version 2.8.2.3 - Penyederhanaan Tampilan Dashboard Guru</h5>
            <small>16 Januari 2025</small>
        </div>
        <div class="card-body">
            <ul class="list-unstyled">
                <li><i class="fas fa-clock text-success"></i> Penyederhanaan tampilan jadwal mengajar hari ini pada dashboard guru</li>
                <li><i class="fas fa-list text-success"></i> Penggabungan tampilan range jam pelajaran dalam satu baris</li>
                <li><i class="fas fa-check text-success"></i> Konsistensi tampilan dengan modul jadwal</li>
            </ul>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">Version 2.8.2.2 - Perbaikan Tampilan Dashboard Guru</h5>
            <small>16 Januari 2025</small>
        </div>
        <div class="card-body">
            <ul class="list-unstyled">
                <li><i class="fas fa-clock text-success"></i> Perbaikan tampilan jadwal mengajar hari ini pada dashboard guru</li>
                <li><i class="fas fa-list text-success"></i> Optimasi format tampilan range jam pelajaran yang tidak berurutan</li>
                <li><i class="fas fa-check text-success"></i> Peningkatan kejelasan informasi jadwal yang terpotong di dashboard</li>
            </ul>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">Version 2.8.2.1 - Perbaikan Tampilan Jadwal</h5>
            <small>16 Januari 2025</small>
        </div>
        <div class="card-body">
            <ul class="list-unstyled">
                <li><i class="fas fa-clock text-success"></i> Perbaikan tampilan jam pelajaran yang tidak berurutan</li>
                <li><i class="fas fa-list text-success"></i> Optimasi format tampilan range jam pelajaran</li>
                <li><i class="fas fa-check text-success"></i> Peningkatan kejelasan informasi jadwal yang terpotong</li>
            </ul>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">Version 2.8.2 - Peningkatan Modul Jadwal Pelajaran</h5>
            <small>16 Januari 2025</small>
        </div>
        <div class="card-body">
            <ul class="list-unstyled">
                <li><i class="fas fa-clock text-success"></i> Implementasi fitur multiple time slots pada jadwal pelajaran</li>
                <li><i class="fas fa-check-square text-success"></i> Perubahan pemilihan jam pelajaran dari radio button ke checkbox</li>
                <li><i class="fas fa-database text-success"></i> Penambahan tabel detail_jadwal_jam untuk menyimpan multiple time slots</li>
                <li><i class="fas fa-trash text-success"></i> Implementasi cascade delete untuk menjaga integritas data jadwal</li>
                <li><i class="fas fa-check text-success"></i> Optimasi tampilan form jadwal untuk mendukung multiple time slots</li>
            </ul>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">Version 2.8.1 - Penyempurnaan Rekap Absensi</h5>
            <small>15 Januari 2025</small>
        </div>
        <div class="card-body">
            <ul class="list-unstyled">
                <li><i class="fas fa-tasks text-success"></i> Perbaikan tampilan status absensi pada rekap detail siswa</li>
                <li><i class="fas fa-info-circle text-success"></i> Penambahan informasi mata pelajaran pada keterangan absensi</li>
                <li><i class="fas fa-check text-success"></i> Optimasi query untuk menampilkan status dan keterangan yang lebih jelas</li>
                <li><i class="fas fa-list text-success"></i> Penyempurnaan format tampilan rekap absensi siswa</li>
            </ul>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">Version 2.8.0 - Peningkatan Modul Manajemen Akun</h5>
            <small>15 Januari 2025</small>
        </div>
        <div class="card-body">
            <ul class="list-unstyled">
                <li><i class="fas fa-user-cog text-success"></i> Penambahan modul manajemen akun untuk admin</li>
                <li><i class="fas fa-user-circle text-success"></i> Penambahan modul profil untuk guru</li>
                <li><i class="fas fa-key text-success"></i> Implementasi fitur ubah password admin</li>
                <li><i class="fas fa-user-edit text-success"></i> Penambahan fitur ubah username (satu kali)</li>
                <li><i class="fas fa-shield-alt text-success"></i> Peningkatan keamanan pada manajemen password</li>
                <li><i class="fas fa-user-lock text-success"></i> Implementasi pembatasan perubahan username</li>
                <li><i class="fas fa-database text-success"></i> Penambahan tracking perubahan username</li>
                <li><i class="fas fa-check text-success"></i> Optimasi integrasi status guru dengan akun</li>
            </ul>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">Version 2.7.2 - Peningkatan Keamanan Modul Absensi dan Laporan</h5>
            <small>15 Januari 2025</small>
        </div>
        <div class="card-body">
            <ul class="list-unstyled">
                <li><i class="fas fa-shield text-success"></i> Perbaikan keamanan modul absensi untuk role guru</li>
                <li><i class="fas fa-shield text-success"></i> Pembatasan akses absensi hanya untuk mata pelajaran yang diampu</li>
                <li><i class="fas fa-filter text-success"></i> Perbaikan filter laporan untuk menampilkan hanya mata pelajaran yang diampu guru</li>
                <li><i class="fas fa-lock text-success"></i> Penambahan validasi keamanan pada export PDF dan Excel</li>
                <li><i class="fas fa-check text-success"></i> Penyesuaian pesan error untuk konsistensi dengan modul lain</li>
            </ul>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">Version 2.7.1 - Penyempurnaan Akses Guru</h5>
            <small>15 Januari 2025</small>
        </div>
        <div class="card-body">
            <ul class="list-unstyled">
                <li><i class="fas fa-shield text-success"></i> Perbaikan filter mata pelajaran untuk guru di modul Tugas</li>
                <li><i class="fas fa-shield text-success"></i> Perbaikan filter mata pelajaran untuk guru di modul Nilai</li>
                <li><i class="fas fa-shield text-success"></i> Perbaikan filter mata pelajaran untuk guru di modul Absensi</li>
                <li><i class="fas fa-code text-success"></i> Optimasi kode dan penghapusan debug messages</li>
                <li><i class="fas fa-check text-success"></i> Perbaikan konsistensi tampilan data untuk role guru</li>
            </ul>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">Version 2.7.0 - Peningkatan Keamanan dan Role-Based Access</h5>
            <small>14 Januari 2025</small>
        </div>
        <div class="card-body">
            <ul class="list-unstyled">
                <li><i class="fas fa-shield text-success"></i> Implementasi middleware untuk role-based access control</li>
                <li><i class="fas fa-shield text-success"></i> Penambahan halaman 403 untuk unauthorized access</li>
                <li><i class="fas fa-lock text-success"></i> Pembatasan akses modul administratif untuk role guru</li>
                <li><i class="fas fa-eye-slash text-success"></i> Penyembunyian menu administratif untuk role guru</li>
                <li><i class="fas fa-filter text-success"></i> Pembatasan tampilan jadwal guru hanya untuk jadwal yang diampu</li>
                <li><i class="fas fa-columns text-success"></i> Penyesuaian tampilan tabel jadwal berdasarkan role</li>
                <li><i class="fas fa-bug-slash text-success"></i> Perbaikan duplikasi session_start pada berbagai modul</li>
                <li><i class="fas fa-check text-success"></i> Optimasi pengecekan session pada middleware</li>
            </ul>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">Version 2.6.0 - Perbaikan Modul Mata Pelajaran dan Kelas</h5>
            <small>14 Januari 2025</small>
        </div>
        <div class="card-body">
            <ul class="list-unstyled">
                <li><i class="fas fa-bug-slash text-success"></i> Perbaikan data retrieval pada edit mata pelajaran</li>
                <li><i class="fas fa-check text-success"></i> Perbaikan urutan loading jQuery di header template</li>
                <li><i class="fas fa-check text-success"></i> Optimasi integrasi DataTables di modul Kelas</li>
                <li><i class="fas fa-check text-success"></i> Perbaikan validasi form dan submission di halaman create kelas</li>
                <li><i class="fas fa-arrow-up text-success"></i> Update jQuery ke versi 3.7.1</li>
                <li><i class="fas fa-arrow-up text-success"></i> Update Bootstrap ke versi 5.3.2</li>
                <li><i class="fas fa-shield text-success"></i> Peningkatan validasi input dan sanitasi</li>
                <li><i class="fas fa-shield text-success"></i> Penguatan pengecekan role-based access control</li>
            </ul>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">Version 2.5 - Modul Data Guru</h5>
            <small>14 Januari 2025</small>
        </div>
        <div class="card-body">
            <ul class="list-unstyled">
                <li><i class="fas fa-plus text-success"></i> Penambahan modul manajemen data guru</li>
                <li><i class="fas fa-plus text-success"></i> Fitur import data guru dari Excel</li>
                <li><i class="fas fa-plus text-success"></i> Sistem aktivasi akun guru dengan username dan password default</li>
                <li><i class="fas fa-plus text-success"></i> Fitur aktivasi dan deaktivasi akun guru</li>
            </ul>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">Version 2.4 - Perbaikan Dashboard</h5>
            <small>13 Januari 2025</small>
        </div>
        <div class="card-body">
            <ul class="list-unstyled">
                <li><i class="fas fa-bug-slash text-success"></i> Perbaikan error getCountByDate di dashboard</li>
                <li><i class="fas fa-check text-success"></i> Penambahan method untuk menghitung total absensi per hari</li>
                <li><i class="fas fa-check text-success"></i> Optimasi query untuk statistik dashboard</li>
            </ul>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">Version 2.3 - Peningkatan UX dan Perbaikan Bug</h5>
            <small>13 Januari 2025</small>
        </div>
        <div class="card-body">
            <ul class="list-unstyled">
                <li><i class="fas fa-check text-success"></i> Data absensi langsung ditampilkan tanpa perlu filter</li>
                <li><i class="fas fa-bug-slash text-success"></i> Perbaikan status absensi pada form edit yang tidak muncul</li>
                <li><i class="fas fa-check text-success"></i> Peningkatan tampilan tabel dengan DataTables</li>
                <li><i class="fas fa-check text-success"></i> Default sorting berdasarkan tanggal terbaru</li>
                <li><i class="fas fa-check text-success"></i> Penyederhanaan filter dan tampilan</li>
                <li><i class="fas fa-check text-success"></i> Penambahan ikon untuk tombol aksi</li>
            </ul>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">Version 2.2 - Perbaikan Sistem Absensi</h5>
            <small>13 Januari 2025</small>
        </div>
        <div class="card-body">
            <ul class="list-unstyled">
                <li><i class="fas fa-bug-slash text-success"></i> Perbaikan tampilan status absensi pada form edit</li>
                <li><i class="fas fa-bug-slash text-success"></i> Perbaikan logika update/insert detail absensi</li>
                <li><i class="fas fa-bug-slash text-success"></i> Perbaikan warning header pada halaman create dan edit</li>
                <li><i class="fas fa-check text-success"></i> Peningkatan validasi dan penanganan error</li>
                <li><i class="fas fa-check text-success"></i> Optimasi query database</li>
                <li><i class="fas fa-check text-success"></i> Perbaikan filter siswa pada halaman nilai tugas</li>
            </ul>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">Version 2.1 - Periode Per Semester</h5>
            <small>13 Januari 2025</small>
        </div>
        <div class="card-body">
            <ul class="list-unstyled">
                <li><i class="fas fa-check text-success"></i> Pemisahan periode untuk semester 1 dan 2</li>
                <li><i class="fas fa-check text-success"></i> Default: Semester 1 (Juli-Desember), Semester 2 (Januari-Juni)</li>
                <li><i class="fas fa-check text-success"></i> Validasi urutan tanggal antar semester</li>
                <li><i class="fas fa-check text-success"></i> Tampilan periode per semester</li>
                <li><i class="fas fa-check text-success"></i> Update otomatis tanggal saat memilih tahun</li>
            </ul>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">Version 2.0 - Periode Tahun Ajaran</h5>
            <small>13 Januari 2025</small>
        </div>
        <div class="card-body">
            <ul class="list-unstyled">
                <li><i class="fas fa-check text-success"></i> Penambahan tanggal mulai dan selesai tahun ajaran</li>
                <li><i class="fas fa-check text-success"></i> Default periode: 1 Juli s/d 30 Juni</li>
                <li><i class="fas fa-check text-success"></i> Validasi tanggal (selesai harus > mulai)</li>
                <li><i class="fas fa-check text-success"></i> Tampilan periode dalam format tanggal Indonesia</li>
                <li><i class="fas fa-check text-success"></i> Update otomatis tanggal saat memilih tahun</li>
            </ul>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">Version 1.9 - Manajemen Tahun Ajaran</h5>
            <small>13 Januari 2025</small>
        </div>
        <div class="card-body">
            <ul class="list-unstyled">
                <li><i class="fas fa-check text-success"></i> Penambahan modul manajemen tahun ajaran</li>
                <li><i class="fas fa-check text-success"></i> Form input tahun ajaran dengan validasi</li>
                <li><i class="fas fa-check text-success"></i> Integrasi dengan modul nilai dan tugas</li>
                <li><i class="fas fa-check text-success"></i> Shortcut untuk menambah tahun ajaran baru</li>
                <li><i class="fas fa-check text-success"></i> Validasi format tahun ajaran (YYYY/YYYY)</li>
            </ul>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">Version 1.8 - Integrasi Periode Aktif</h5>
            <small>13 Januari 2025</small>
        </div>
        <div class="card-body">
            <ul class="list-unstyled">
                <li><i class="fas fa-check text-success"></i> Integrasi periode aktif dengan modul nilai dan tugas</li>
                <li><i class="fas fa-check text-success"></i> Otomatis menggunakan periode aktif sebagai default</li>
                <li><i class="fas fa-check text-success"></i> Opsi untuk memilih periode lain</li>
                <li><i class="fas fa-check text-success"></i> Notifikasi saat melihat periode non-aktif</li>
                <li><i class="fas fa-check text-success"></i> Tombol kembali ke periode aktif</li>
            </ul>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">Version 1.7 - Periode Aktif</h5>
            <small>13 Januari 2025</small>
        </div>
        <div class="card-body">
            <ul class="list-unstyled">
                <li><i class="fas fa-check text-success"></i> Penambahan modul pengaturan periode aktif</li>
                <li><i class="fas fa-check text-success"></i> Manajemen semester dan tahun ajaran aktif</li>
                <li><i class="fas fa-check text-success"></i> Tampilan periode aktif di header</li>
                <li><i class="fas fa-check text-success"></i> Riwayat perubahan periode</li>
                <li><i class="fas fa-check text-success"></i> Otomatisasi periode default berdasarkan tanggal</li>
            </ul>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">Version 1.6 - Export Nilai</h5>
            <small>13 Januari 2025</small>
        </div>
        <div class="card-body">
            <ul class="list-unstyled">
                <li><i class="fas fa-check text-success"></i> Penambahan fitur export nilai ke Excel</li>
                <li><i class="fas fa-check text-success"></i> Export nilai per mata pelajaran</li>
                <li><i class="fas fa-check text-success"></i> Export nilai per kelas</li>
                <li><i class="fas fa-check text-success"></i> Format Excel yang rapi dengan header dan keterangan</li>
            </ul>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">Version 1.5 - Integrasi Nilai Absensi</h5>
            <small>13 Januari 2025</small>
        </div>
        <div class="card-body">
            <ul class="list-unstyled">
                <li><i class="fas fa-check text-success"></i> Penambahan perhitungan nilai absensi otomatis</li>
                <li><i class="fas fa-check text-success"></i> Integrasi dengan modul absensi untuk nilai kehadiran</li>
                <li><i class="fas fa-check text-success"></i> Perhitungan nilai berdasarkan status kehadiran:
                    <ul>
                        <li>Hadir: 100%</li>
                        <li>Sakit: 75%</li>
                        <li>Izin: 50%</li>
                        <li>Alpha: 0%</li>
                    </ul>
                </li>
                <li><i class="fas fa-check text-success"></i> Nilai absensi dihitung per semester</li>
                <li><i class="fas fa-check text-success"></i> Tampilan nilai absensi otomatis di form input nilai</li>
            </ul>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">Version 1.4 - Sistem Penilaian</h5>
            <small>13 Januari 2025</small>
        </div>
        <div class="card-body">
            <ul class="list-unstyled">
                <li><i class="fas fa-check text-success"></i> Penambahan modul sistem penilaian</li>
                <li><i class="fas fa-check text-success"></i> Integrasi dengan data mata pelajaran</li>
                <li><i class="fas fa-check text-success"></i> Fitur input nilai (Tugas, UTS, UAS)</li>
                <li><i class="fas fa-check text-success"></i> Perhitungan nilai akhir otomatis (30% Tugas, 30% UTS, 40% UAS)</li>
                <li><i class="fas fa-check text-success"></i> Tampilan data nilai per mata pelajaran</li>
            </ul>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">Version 1.3 - Authentication System</h5>
            <small>13 Januari 2025</small>
        </div>
        <div class="card-body">
            <ul class="list-unstyled">
                <li><i class="fas fa-check text-success"></i> Penambahan sistem login untuk keamanan aplikasi</li>
                <li><i class="fas fa-check text-success"></i> Penambahan manajemen pengguna (admin dan guru)</li>
                <li><i class="fas fa-check text-success"></i> Penambahan fitur logout</li>
                <li><i class="fas fa-check text-success"></i> Peningkatan keamanan dengan session management</li>
            </ul>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">Version 1.2 - Dashboard Update</h5>
            <small>13 Januari 2025</small>
        </div>
        <div class="card-body">
            <ul class="list-unstyled">
                <li><i class="fas fa-check text-success"></i> Penambahan informasi versi pada footer</li>
            </ul>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">Version 1.1 - Template Update</h5>
            <small>13 Januari 2025</small>
        </div>
        <div class="card-body">
            <ul class="list-unstyled">
                <li><i class="fas fa-check text-success"></i> Perubahan layout template dari top menu menjadi sidebar</li>
                <li><i class="fas fa-check text-success"></i> Penambahan menu Dashboard di sidebar</li>
                <li><i class="fas fa-check text-success"></i> Penambahan modul Changelog</li>
                <li><i class="fas fa-check text-success"></i> Perbaikan tampilan responsif untuk perangkat mobile</li>
            </ul>
        </div>
    </div>

    <div class="card">
        <div class="card-header bg-secondary text-white">
            <h5 class="mb-0">Version 1.0 - Initial Release</h5>
            <small>Versi Awal</small>
        </div>
        <div class="card-body">
            <ul class="list-unstyled">
                <li><i class="fas fa-check text-success"></i> Sistem manajemen data kelas</li>
                <li><i class="fas fa-check text-success"></i> Sistem manajemen data siswa</li>
                <li><i class="fas fa-check text-success"></i> Sistem manajemen mata pelajaran</li>
                <li><i class="fas fa-check text-success"></i> Sistem manajemen jadwal pelajaran</li>
                <li><i class="fas fa-check text-success"></i> Sistem absensi harian</li>
                <li><i class="fas fa-check text-success"></i> Sistem rekap absensi</li>
                <li><i class="fas fa-check text-success"></i> Template dengan navigasi menu atas</li>
            </ul>
        </div>
    </div>
</div>

<?php
include '../template/footer.php';
?>
