<?php
require_once __DIR__ . '/../middleware/auth.php';
checkGuruAccess();
require_once '../template/header.php';
require_once '../config/database.php';
require_once '../models/Rpp.php';
require_once '../models/RppQuestion.php';
require_once '../models/ExamBlueprint.php';
require_once '../models/Guru.php';

// Validasi parameter
if (!isset($_GET['rpp_id'])) {
    $_SESSION['error'] = "ID RPP tidak ditemukan.";
    header("Location: generate_questions.php");
    exit();
}

$rpp_id = $_GET['rpp_id'];

// Dapatkan guru_id
$guru = new Guru();
$stmt = $guru->getByUserId($_SESSION['user_id']);
if ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
    $guru_id = $row['id'];
    $guru_data = $row;
} else {
    die("Data guru tidak ditemukan");
}

// Ambil data RPP
$rpp = new Rpp();
$stmt = $rpp->getOne($rpp_id);
$rpp_data = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$rpp_data || $rpp_data['guru_id'] != $guru_id) {
    $_SESSION['error'] = "RPP tidak ditemukan atau bukan milik Anda.";
    header("Location: generate_questions.php");
    exit();
}

// Ambil soal-soal RPP
$rppQuestion = new RppQuestion();
$stmt = $rppQuestion->getByRppId($rpp_id);
$questions_data = $stmt->fetchAll(PDO::FETCH_ASSOC);

if (empty($questions_data)) {
    $_SESSION['error'] = "Tidak ada soal untuk membuat kisi-kisi. Silakan generate soal terlebih dahulu.";
    header("Location: generate_questions.php?rpp_id=" . $rpp_id);
    exit();
}

// Check if blueprint already exists
$examBlueprint = new ExamBlueprint();
$existing_blueprint_stmt = $examBlueprint->getByRppId($rpp_id);
$existing_blueprint = $existing_blueprint_stmt->fetch(PDO::FETCH_ASSOC);

$success_msg = isset($_SESSION['success']) ? $_SESSION['success'] : '';
$error_msg = isset($_SESSION['error']) ? $_SESSION['error'] : '';
$generation_error = ''; // Initialize the generation error variable
unset($_SESSION['success'], $_SESSION['error']);
?>

<div class="container-fluid">
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">
                <i class="fas fa-file-alt"></i> Generate Kisi-kisi RPP
            </h5>
            <div>
                <a href="generate_questions.php?rpp_id=<?= $rpp_id ?>" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Kembali ke Soal
                </a>
            </div>
        </div>
        <div class="card-body">
            <?php if ($success_msg): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?= $success_msg ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <?php if ($error_msg): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?= $error_msg ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <?php if ($generation_error): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <h6><i class="fas fa-exclamation-triangle"></i> Error Generate Kisi-kisi</h6>
                    <p class="mb-0"><?= htmlspecialchars($generation_error) ?></p>
                </div>
            <?php endif; ?>

            <!-- Loading State (Hidden by default) -->
            <div id="loadingState" class="text-center py-5" style="display: none;">
                <div class="spinner-border text-primary" role="status" style="width: 3rem; height: 3rem;">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <h5 class="mt-3">Generating Blueprint...</h5>
                <p class="text-muted">AI sedang membuat kisi-kisi ujian dari RPP dan <?= count($questions_data) ?> soal. Mohon tunggu...</p>
                <div class="progress mt-3" style="max-width: 400px; margin: 0 auto;">
                    <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 100%"></div>
                </div>
            </div>

            <!-- Generated Blueprint Display (Hidden by default) -->
            <div id="generatedBlueprintSection" style="display: none;">
                <div class="alert alert-success">
                    <h6><i class="fas fa-check-circle"></i> Kisi-kisi Berhasil Di-generate</h6>
                    <p class="mb-0">Kisi-kisi ujian RPP telah berhasil dibuat. Silakan review dan simpan jika sudah sesuai.</p>
                </div>

                <form action="single_blueprint_save.php" method="POST" id="saveBlueprintForm">
                    <input type="hidden" name="rpp_id" value="<?= $rpp_id ?>">
                    <input type="hidden" name="blueprint_data" id="blueprintDataInput">

                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-info-circle"></i> Preview Kisi-kisi</h6>
                        </div>
                        <div class="card-body" id="blueprintPreviewContainer">
                            <!-- Blueprint preview will be loaded here -->
                        </div>
                    </div>

                    <div class="text-center">
                        <button type="submit" class="btn btn-success btn-lg">
                            <i class="fas fa-save"></i> Simpan Kisi-kisi
                        </button>
                        <button type="button" class="btn btn-outline-secondary btn-lg" onclick="resetForm()">
                            <i class="fas fa-redo"></i> Generate Ulang
                        </button>
                    </div>
                </form>
            </div>

            <!-- Main Form (Visible by default) -->
            <!-- RPP Information -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-info-circle"></i> Informasi RPP</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless table-sm">
                                <tr>
                                    <td width="150"><strong>Mata Pelajaran:</strong></td>
                                    <td><?= htmlspecialchars($rpp_data['nama_mapel']) ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Tema/Subtema:</strong></td>
                                    <td><?= htmlspecialchars($rpp_data['tema_subtema']) ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Kelas:</strong></td>
                                    <td><?= htmlspecialchars($rpp_data['nama_kelas']) ?></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless table-sm">
                                <tr>
                                    <td width="150"><strong>Total Soal:</strong></td>
                                    <td><?= count($questions_data) ?> soal</td>
                                </tr>
                                <tr>
                                    <td><strong>Materi Pokok:</strong></td>
                                    <td><?= htmlspecialchars($rpp_data['materi_pokok']) ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Status:</strong></td>
                                    <td><span class="badge bg-success">Siap Generate</span></td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Existing Blueprint Check -->
            <?php if ($existing_blueprint): ?>
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle"></i> Kisi-kisi Sudah Ada</h6>
                    <p class="mb-2">Kisi-kisi untuk RPP ini sudah pernah dibuat pada <?= date('d/m/Y H:i', strtotime($existing_blueprint['created_at'])) ?>.</p>
                    <div class="d-flex gap-2">
                        <a href="single_blueprint_result.php?blueprint_id=<?= $existing_blueprint['id'] ?>" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-eye"></i> Lihat Kisi-kisi
                        </a>
                        <a href="single_blueprint_export.php?blueprint_id=<?= $existing_blueprint['id'] ?>&format=pdf" class="btn btn-sm btn-outline-danger" target="_blank">
                            <i class="fas fa-file-pdf"></i> Export PDF
                        </a>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Blueprint Generation Form -->
            <div id="mainFormSection">
                <form method="POST" id="blueprintForm">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-cog"></i> Konfigurasi Kisi-kisi</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="blueprint_title" class="form-label">Judul Kisi-kisi</label>
                                        <input type="text" class="form-control" id="blueprint_title" name="blueprint_title" 
                                               value="<?= htmlspecialchars($rpp_data['tema_subtema']) ?> - Kisi-kisi" required>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="exam_type" class="form-label">Jenis Ujian</label>
                                        <select class="form-select" id="exam_type" name="exam_type" required>
                                            <option value="Ujian Harian">Ujian Harian</option>
                                            <option value="Ujian Tengah Semester">Ujian Tengah Semester</option>
                                            <option value="Ujian Akhir Semester">Ujian Akhir Semester</option>
                                            <option value="Ujian Praktik">Ujian Praktik</option>
                                        </select>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="semester" class="form-label">Semester</label>
                                        <select class="form-select" id="semester" name="semester" required>
                                            <option value="Ganjil">Ganjil</option>
                                            <option value="Genap">Genap</option>
                                        </select>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="exam_duration" class="form-label">Durasi Ujian (menit)</label>
                                        <input type="number" class="form-control" id="exam_duration" name="exam_duration" 
                                               value="90" min="30" max="180" required>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="total_score" class="form-label">Total Skor</label>
                                        <input type="number" class="form-control" id="total_score" name="total_score" 
                                               value="100" min="50" max="200" required>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="additional_notes" class="form-label">Catatan Tambahan</label>
                                        <textarea class="form-control" id="additional_notes" name="additional_notes" 
                                                  rows="3" placeholder="Catatan khusus untuk ujian ini..."></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="text-center">
                        <button type="submit" name="generate_blueprint" class="btn btn-primary btn-lg">
                            <i class="fas fa-magic"></i> Generate Kisi-kisi RPP
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Form validation and AJAX submission
    document.getElementById('blueprintForm')?.addEventListener('submit', function(e) {
        e.preventDefault(); // Prevent default form submission

        // Show loading state
        showLoadingState();

        // Prepare form data
        const formData = new FormData(this);
        formData.append('action', 'generate_blueprint');
        formData.append('rpp_id', '<?= $rpp_id ?>');

        // Make AJAX request
        fetch('process_blueprint.php', {
            method: 'POST',
            body: formData
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            hideLoadingState();

            // Debug logging
            console.log('Blueprint AJAX Response:', data);
            console.log('Blueprint Data:', data.blueprint);
            console.log('Blueprint Debug Info:', data.debug_info);

            if (data.success) {
                if (!data.blueprint || (typeof data.blueprint === 'object' && Object.keys(data.blueprint).length === 0)) {
                    console.error('Blueprint data is empty or null:', data.blueprint);
                    showError('Blueprint generation returned empty data. Check server logs for details.');
                } else {
                    showGeneratedBlueprint(data.blueprint);
                }
            } else {
                let errorMessage = data.error || 'Terjadi kesalahan saat generate kisi-kisi';

                // Add debug info if available
                if (data.debug_info) {
                    console.error('Debug Info:', data.debug_info);
                    errorMessage += ` (Debug: ${data.debug_info.file}:${data.debug_info.line})`;
                }

                showError(errorMessage);
            }
        })
        .catch(error => {
            hideLoadingState();
            console.error('AJAX Error:', error);
            showError(`Terjadi kesalahan koneksi: ${error.message}. Silakan coba lagi.`);
        });
    });
});

function showLoadingState() {
    document.getElementById('blueprintForm').style.display = 'none';
    document.getElementById('loadingState').style.display = 'block';
    document.getElementById('generatedBlueprintSection').style.display = 'none';
}

function hideLoadingState() {
    document.getElementById('loadingState').style.display = 'none';
}

function showGeneratedBlueprint(blueprint) {
    console.log('showGeneratedBlueprint called with:', blueprint);

    // Store blueprint data
    document.getElementById('blueprintDataInput').value = JSON.stringify(blueprint);

    // Generate preview HTML using the same template as Multi-RPP
    const previewHtml = generateBlueprintPreview(blueprint);
    console.log('Generated preview HTML length:', previewHtml.length);
    console.log('Preview HTML preview:', previewHtml.substring(0, 200) + '...');

    document.getElementById('blueprintPreviewContainer').innerHTML = previewHtml;

    // Show the generated section
    document.getElementById('generatedBlueprintSection').style.display = 'block';

    console.log('Blueprint section should now be visible');
}

function showError(message) {
    // Create error alert
    const errorAlert = document.createElement('div');
    errorAlert.className = 'alert alert-danger alert-dismissible fade show';
    errorAlert.innerHTML = `
        <h6><i class="fas fa-exclamation-triangle"></i> Error Generate Kisi-kisi</h6>
        <p class="mb-0">${message}</p>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;

    // Insert error before the form
    const form = document.getElementById('blueprintForm');
    form.parentNode.insertBefore(errorAlert, form);

    // Show the form again
    form.style.display = 'block';
}

function resetForm() {
    document.getElementById('generatedBlueprintSection').style.display = 'none';
    document.getElementById('blueprintForm').style.display = 'block';

    // Remove any error alerts
    const alerts = document.querySelectorAll('.alert-danger');
    alerts.forEach(alert => alert.remove());
}

// Use the same blueprint preview generation function as Multi-RPP
function generateBlueprintPreview(blueprint) {
    let html = '<div class="blueprint-preview">';

    // Exam Information
    if (blueprint.exam_info) {
        html += `
            <div class="row mb-4">
                <div class="col-md-12">
                    <h5 class="text-center mb-3">${blueprint.exam_info.title || 'Kisi-kisi Ujian'}</h5>
                    <table class="table table-bordered">
                        <tr>
                            <td width="150"><strong>Mata Pelajaran</strong></td>
                            <td>${blueprint.exam_info.subject || ''}</td>
                            <td width="150"><strong>Jenis Ujian</strong></td>
                            <td>${blueprint.exam_info.type || ''}</td>
                        </tr>
                        <tr>
                            <td><strong>Durasi</strong></td>
                            <td>${blueprint.exam_info.duration || ''}</td>
                            <td><strong>Total Skor</strong></td>
                            <td>${blueprint.exam_info.total_score || ''}</td>
                        </tr>
                    </table>
                </div>
            </div>
        `;
    }

    // Learning Objectives
    if (blueprint.learning_objectives && blueprint.learning_objectives.length > 0) {
        html += `
            <div class="mb-4">
                <h6><i class="fas fa-bullseye"></i> Tujuan Pembelajaran</h6>
                <div class="table-responsive">
                    <table class="table table-striped table-sm">
                        <thead class="table-dark">
                            <tr>
                                <th>Chapter</th>
                                <th>Tujuan Pembelajaran</th>
                            </tr>
                        </thead>
                        <tbody>
        `;

        blueprint.learning_objectives.forEach(objective => {
            html += `
                <tr>
                    <td><strong>${objective.chapter || ''}</strong></td>
                    <td>
            `;
            if (objective.objectives && Array.isArray(objective.objectives)) {
                html += '<ul class="mb-0">';
                objective.objectives.forEach(obj => {
                    html += `<li>${obj}</li>`;
                });
                html += '</ul>';
            }
            html += `
                    </td>
                </tr>
            `;
        });

        html += `
                        </tbody>
                    </table>
                </div>
            </div>
        `;
    }

    // Cognitive Mapping
    if (blueprint.cognitive_mapping) {
        html += `
            <div class="mb-4">
                <h6><i class="fas fa-brain"></i> Pemetaan Level Kognitif</h6>
                <div class="table-responsive">
                    <table class="table table-bordered table-sm">
                        <thead class="table-dark">
                            <tr>
                                <th>Level</th>
                                <th>Jumlah Soal</th>
                                <th>Persentase</th>
                            </tr>
                        </thead>
                        <tbody>
        `;

        const cognitiveDescriptions = {
            'C1': 'Mengingat (Remember)',
            'C2': 'Memahami (Understand)',
            'C3': 'Menerapkan (Apply)',
            'C4': 'Menganalisis (Analyze)',
            'C5': 'Mengevaluasi (Evaluate)',
            'C6': 'Mencipta (Create)'
        };

        const totalQuestions = Object.values(blueprint.cognitive_mapping).reduce((sum, count) => sum + count, 0);

        Object.entries(blueprint.cognitive_mapping).forEach(([level, count]) => {
            if (count > 0) {
                const percentage = totalQuestions > 0 ? Math.round((count / totalQuestions) * 100 * 10) / 10 : 0;
                html += `
                    <tr>
                        <td><strong>${level}</strong> - ${cognitiveDescriptions[level] || level}</td>
                        <td class="text-center">${count}</td>
                        <td class="text-center">${percentage}%</td>
                    </tr>
                `;
            }
        });

        html += `
                        </tbody>
                    </table>
                </div>
            </div>
        `;
    }

    // Blueprint Table
    if (blueprint.blueprint_table && blueprint.blueprint_table.length > 0) {
        html += `
            <div class="mb-4">
                <h6><i class="fas fa-table"></i> Tabel Kisi-kisi</h6>
                <div class="table-responsive">
                    <table class="table table-bordered table-sm">
                        <thead class="table-dark">
                            <tr>
                                <th>Chapter</th>
                                <th>Tujuan Pembelajaran</th>
                                <th>Indikator</th>
                                <th>Level Kognitif</th>
                                <th>No. Soal</th>
                                <th>Jumlah</th>
                            </tr>
                        </thead>
                        <tbody>
        `;

        blueprint.blueprint_table.forEach(row => {
            html += `
                <tr>
                    <td><strong>${row.chapter || ''}</strong></td>
                    <td>${row.learning_objective || ''}</td>
                    <td>${row.indicator || ''}</td>
                    <td class="text-center">${row.cognitive_level || ''}</td>
                    <td class="text-center">
                        ${Array.isArray(row.question_numbers) ? row.question_numbers.join(', ') : (row.question_numbers || '')}
                    </td>
                    <td class="text-center">${row.total_questions || 1}</td>
                </tr>
            `;
        });

        html += `
                        </tbody>
                    </table>
                </div>
            </div>
        `;
    }

    // Summary
    if (blueprint.summary) {
        html += `
            <div class="mb-4">
                <h6><i class="fas fa-chart-bar"></i> Ringkasan</h6>
                <div class="row">
                    <div class="col-md-4">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <h5 class="card-title">${blueprint.summary.total_questions || 0}</h5>
                                <p class="card-text">Total Soal</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <h5 class="card-title">1</h5>
                                <p class="card-text">Total Chapter</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <h5 class="card-title">
                                    ${blueprint.summary.multiple_choice || 0} : ${blueprint.summary.essay || 0}
                                </h5>
                                <p class="card-text">PG : Essay</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    html += '</div>';
    return html;
}
</script>

<style>
.table th {
    font-weight: 600;
    font-size: 0.9rem;
}

.table td {
    vertical-align: middle;
    font-size: 0.9rem;
}

/* Fix for table-dark header visibility */
.table-dark th {
    background-color: #212529 !important;
    color: #ffffff !important;
    border-color: #32383e !important;
}

.table-dark th,
.table-dark td {
    border-color: #32383e !important;
}

/* Ensure blueprint preview table headers are visible */
.blueprint-preview .table-dark th {
    background-color: #343a40 !important;
    color: #ffffff !important;
    font-weight: 600;
    text-align: center;
}

.blueprint-preview .table th {
    font-weight: 600;
    font-size: 0.9rem;
    color: inherit;
}

.card-title {
    color: #495057;
}

.form-check {
    margin-bottom: 0.5rem;
}

.spinner-border {
    animation: spinner-border 0.75s linear infinite;
}

@keyframes spinner-border {
    to {
        transform: rotate(360deg);
    }
}
</style>

<?php require_once '../template/footer.php'; ?>
