<?php
require_once __DIR__ . '/../middleware/auth.php';
checkGuruAccess();
require_once '../template/header.php';
require_once '../config/database.php';
require_once '../models/Rpp.php';
require_once '../models/Guru.php';
require_once '../models/GeminiApi.php';

// Cek parameter rpp_id
if (!isset($_GET['rpp_id'])) {
    $_SESSION['error'] = "ID RPP tidak ditemukan.";
    header("Location: generate_questions.php");
    exit();
}

$rpp_id = $_GET['rpp_id'];

// Dapatkan guru_id dari tabel guru berdasarkan user_id yang login
$guru = new Guru();
$stmt = $guru->getByUserId($_SESSION['user_id']);
if ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
    $guru_id = $row['id'];
} else {
    die("Data guru tidak ditemukan");
}

$rpp = new Rpp();

// Ambil data RPP dan pastikan milik guru yang login
$stmt = $rpp->getOne($rpp_id);
$rpp_data = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$rpp_data || $rpp_data['guru_id'] != $guru_id) {
    $_SESSION['error'] = "RPP tidak ditemukan atau bukan milik Anda.";
    header("Location: generate_questions.php");
    exit();
}

// Test Gemini API connection
$geminiApi = new GeminiApi();
$api_test = $geminiApi->testConnection();

$error_msg = isset($_SESSION['error']) ? $_SESSION['error'] : '';
unset($_SESSION['error']);
?>

<div class="container-fluid">
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">Konfigurasi Generate Soal</h5>
            <a href="generate_questions.php" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Kembali
            </a>
        </div>
        <div class="card-body">
            <?php if ($error_msg): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?= $error_msg ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <?php if (!$api_test['success']): ?>
                <div class="alert alert-danger">
                    <h6><i class="fas fa-exclamation-triangle"></i> Masalah Koneksi API</h6>
                    <p class="mb-0"><?= htmlspecialchars($api_test['message']) ?></p>
                    <small class="text-muted">Silakan hubungi administrator untuk mengkonfigurasi API key Gemini.</small>
                </div>
            <?php endif; ?>

            <!-- RPP Information -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-file-alt"></i> Informasi RPP</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>Mata Pelajaran:</strong> <?= htmlspecialchars($rpp_data['nama_mapel']) ?></p>
                            <p><strong>Kelas:</strong> <?= htmlspecialchars($rpp_data['nama_kelas']) ?></p>
                            <p><strong>Materi Pokok:</strong> <?= htmlspecialchars($rpp_data['materi_pokok']) ?></p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>Semester:</strong> <?= htmlspecialchars($rpp_data['semester']) ?></p>
                            <p><strong>Tahun Ajaran:</strong> <?= htmlspecialchars($rpp_data['tahun_ajaran']) ?></p>
                            <p><strong>Tema/Subtema:</strong> <?= htmlspecialchars($rpp_data['tema_subtema'] ?: '-') ?></p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Configuration Form -->
            <form id="generateForm" action="preview_questions_async.php" method="POST">
                <input type="hidden" name="rpp_id" value="<?= $rpp_id ?>">
                
                <div class="row">
                    <div class="col-md-8">
                        <!-- Question Types -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="fas fa-question-circle"></i> Jenis Soal</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="multiple_choice_count" class="form-label">Soal Pilihan Ganda</label>
                                            <input type="number" class="form-control" id="multiple_choice_count" name="multiple_choice_count" 
                                                   min="0" max="10" value="5" onchange="updateTotal()">
                                        </div>
                                        
                                        <div class="mb-3" id="mc_options_group">
                                            <label for="multiple_choice_options" class="form-label">Jumlah Opsi Jawaban</label>
                                            <select class="form-select" id="multiple_choice_options" name="multiple_choice_options">
                                                <option value="2">A-B (2 opsi)</option>
                                                <option value="3">A-C (3 opsi)</option>
                                                <option value="4" selected>A-D (4 opsi)</option>
                                                <option value="5">A-E (5 opsi)</option>
                                            </select>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="essay_count" class="form-label">Soal Essay</label>
                                            <input type="number" class="form-control" id="essay_count" name="essay_count" 
                                                   min="0" max="10" value="2" onchange="updateTotal()">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Difficulty Levels -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="fas fa-layer-group"></i> Tingkat Kesulitan</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="regular_count" class="form-label">Regular (C1-C3)</label>
                                            <input type="number" class="form-control" id="regular_count" name="regular_count"
                                                   min="0" max="10" value="4" onchange="updateTotal()">
                                            <small class="text-muted">Mengingat, Memahami, Menerapkan</small>
                                        </div>

                                        <div class="mb-3">
                                            <label for="hots_easy_count" class="form-label">HOTS Mudah (C4)</label>
                                            <input type="number" class="form-control" id="hots_easy_count" name="hots_easy_count"
                                                   min="0" max="10" value="2" onchange="updateTotal()">
                                            <small class="text-muted">Menganalisis - Level Dasar</small>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="hots_medium_count" class="form-label">HOTS Sedang (C4-C5)</label>
                                            <input type="number" class="form-control" id="hots_medium_count" name="hots_medium_count"
                                                   min="0" max="10" value="1" onchange="updateTotal()">
                                            <small class="text-muted">Menganalisis, Mengevaluasi</small>
                                        </div>

                                        <div class="mb-3">
                                            <label for="hots_hard_count" class="form-label">HOTS Tinggi (C5-C6)</label>
                                            <input type="number" class="form-control" id="hots_hard_count" name="hots_hard_count"
                                                   min="0" max="10" value="0" onchange="updateTotal()">
                                            <small class="text-muted">Mengevaluasi, Mencipta</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="d-grid">
                            <?php if (!$api_test['success']): ?>
                                <button type="button" class="btn btn-danger btn-lg" disabled>
                                    <i class="fas fa-exclamation-triangle"></i> API Connection Failed - Cannot Generate
                                </button>
                                <small class="text-muted text-center mt-2">
                                    <i class="fas fa-info-circle"></i> Hubungi administrator untuk mengkonfigurasi API key Gemini
                                </small>
                            <?php else: ?>
                                <button type="submit" class="btn btn-primary btn-lg" id="generateBtn">
                                    <i class="fas fa-magic"></i> Generate Soal
                                </button>
                            <?php endif; ?>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <!-- Summary -->
                        <div class="card bg-light">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="fas fa-calculator"></i> Ringkasan</h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <div class="d-flex justify-content-between">
                                        <span>Total Soal:</span>
                                        <span class="fw-bold" id="total_questions">7</span>
                                    </div>
                                    <div class="progress mt-1">
                                        <div class="progress-bar" role="progressbar" id="progress_bar" style="width: 70%"></div>
                                    </div>
                                    <small class="text-muted">Maksimal 10 soal</small>
                                </div>

                                <hr>

                                <div class="mb-2">
                                    <div class="d-flex justify-content-between">
                                        <span>Pilihan Ganda:</span>
                                        <span id="summary_mc">5</span>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <div class="d-flex justify-content-between">
                                        <span>Essay:</span>
                                        <span id="summary_essay">2</span>
                                    </div>
                                </div>

                                <hr>

                                <div class="mb-2">
                                    <div class="d-flex justify-content-between">
                                        <span>Regular (C1-C3):</span>
                                        <span id="summary_regular">4</span>
                                    </div>
                                </div>
                                <div class="mb-2">
                                    <div class="d-flex justify-content-between">
                                        <span>HOTS Mudah (C4):</span>
                                        <span id="summary_hots_easy">2</span>
                                    </div>
                                </div>
                                <div class="mb-2">
                                    <div class="d-flex justify-content-between">
                                        <span>HOTS Sedang (C4-C5):</span>
                                        <span id="summary_hots_medium">1</span>
                                    </div>
                                </div>
                                <div class="mb-2">
                                    <div class="d-flex justify-content-between">
                                        <span>HOTS Tinggi (C5-C6):</span>
                                        <span id="summary_hots_hard">0</span>
                                    </div>
                                </div>

                                <div class="alert alert-info small mt-3">
                                    <i class="fas fa-info-circle"></i>
                                    Pastikan total soal tidak melebihi 10 dan sesuai dengan kebutuhan Anda.
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function updateTotal() {
    const mcCount = parseInt(document.getElementById('multiple_choice_count').value) || 0;
    const essayCount = parseInt(document.getElementById('essay_count').value) || 0;
    const regularCount = parseInt(document.getElementById('regular_count').value) || 0;
    const hotsEasyCount = parseInt(document.getElementById('hots_easy_count').value) || 0;
    const hotsMediumCount = parseInt(document.getElementById('hots_medium_count').value) || 0;
    const hotsHardCount = parseInt(document.getElementById('hots_hard_count').value) || 0;

    const totalByType = mcCount + essayCount;
    const totalByDifficulty = regularCount + hotsEasyCount + hotsMediumCount + hotsHardCount;

    // Update summary
    document.getElementById('summary_mc').textContent = mcCount;
    document.getElementById('summary_essay').textContent = essayCount;
    document.getElementById('summary_regular').textContent = regularCount;
    document.getElementById('summary_hots_easy').textContent = hotsEasyCount;
    document.getElementById('summary_hots_medium').textContent = hotsMediumCount;
    document.getElementById('summary_hots_hard').textContent = hotsHardCount;

    // Use the higher total for validation
    const maxTotal = Math.max(totalByType, totalByDifficulty);
    document.getElementById('total_questions').textContent = maxTotal;

    // Update progress bar
    const progressPercent = (maxTotal / 10) * 100;
    const progressBar = document.getElementById('progress_bar');
    progressBar.style.width = progressPercent + '%';
    
    const apiAvailable = <?= $api_test['success'] ? 'true' : 'false' ?>;
    const generateBtn = document.getElementById('generateBtn');

    if (!apiAvailable) {
        // API not available - keep button disabled regardless of total
        if (generateBtn) {
            generateBtn.disabled = true;
        }
        progressBar.className = 'progress-bar bg-warning';
    } else if (maxTotal > 10) {
        progressBar.className = 'progress-bar bg-danger';
        if (generateBtn) generateBtn.disabled = true;
    } else if (maxTotal === 0) {
        progressBar.className = 'progress-bar bg-secondary';
        if (generateBtn) generateBtn.disabled = true;
    } else {
        progressBar.className = 'progress-bar bg-success';
        if (generateBtn) generateBtn.disabled = false;
    }

    // Show/hide multiple choice options
    const mcOptionsGroup = document.getElementById('mc_options_group');
    mcOptionsGroup.style.display = mcCount > 0 ? 'block' : 'none';
}

// Initialize
document.addEventListener('DOMContentLoaded', function() {
    updateTotal();

    // Check API availability and update button states
    const apiAvailable = <?= $api_test['success'] ? 'true' : 'false' ?>;

    // Add form validation
    const generateForm = document.getElementById('generateForm');
    if (generateForm) {
        generateForm.addEventListener('submit', function(e) {
            if (!apiAvailable) {
                e.preventDefault();
                alert('API tidak tersedia. Silakan hubungi administrator.');
                return false;
            }

            const total = parseInt(document.getElementById('total_questions').textContent);
            if (total > 10 || total === 0) {
                e.preventDefault();
                alert('Total soal harus antara 1-10');
                return false;
            }
        });
    }

    // Update button states based on API availability
    updateButtonStates();
});

function updateButtonStates() {
    const apiAvailable = <?= $api_test['success'] ? 'true' : 'false' ?>;
    const generateBtn = document.getElementById('generateBtn');

    if (!apiAvailable && generateBtn) {
        generateBtn.disabled = true;
        generateBtn.innerHTML = '<i class="fas fa-exclamation-triangle"></i> API Connection Failed - Cannot Generate';
        generateBtn.className = 'btn btn-danger btn-lg';
    }
}
</script>

<?php require_once '../template/footer.php'; ?>
