<?php
require_once '../config/database.php';
require_once '../models/MataPelajaran.php';
require_once '../models/Nilai.php';
require_once '../models/Siswa.php';
require_once '../models/Kelas.php';
require_once '../vendor/autoload.php';

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

// Check if required parameters exist
if(!isset($_GET['mapel_id']) || !isset($_GET['semester']) || !isset($_GET['tahun_ajaran'])) {
    die("Parameter tidak lengkap");
}

$mapel_id = $_GET['mapel_id'];
$semester = $_GET['semester'];
$tahun_ajaran = $_GET['tahun_ajaran'];

// Get mata pelajaran details
$mapel = new MataPelajaran();
$mapel->id = $mapel_id;
$mapel->getOne();

// Get kelas details if specified
$kelas_id = isset($_GET['kelas_id']) ? $_GET['kelas_id'] : null;
$kelas = null;
if($kelas_id) {
    $kelas = new Kelas();
    $kelas->id = $kelas_id;
    $kelas->getOne();
}

// Create new Spreadsheet object
$spreadsheet = new Spreadsheet();
$sheet = $spreadsheet->getActiveSheet();

// Set application info and headers
$sheet->setCellValue('A1', 'SIHADIR - Sistem Informasi Kehadiran Siswa | Versi 2.18.0');
$sheet->setCellValue('A2', 'DAFTAR NILAI ' . $mapel->nama_mapel);
$sheet->setCellValue('A3', 'Semester ' . $semester . ' - Tahun Ajaran ' . $tahun_ajaran);
$sheet->setCellValue('A4', 'KKM: ' . $mapel->kkm);

// Merge header cells
$sheet->mergeCells('A1:J1');
$sheet->mergeCells('A2:J2');
$sheet->mergeCells('A3:J3');
$sheet->mergeCells('A4:J4');

// Style header
$headerStyle = [
    'font' => [
        'bold' => true,
        'size' => 14
    ],
    'alignment' => [
        'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
        'vertical' => \PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER,
    ]
];
$sheet->getStyle('A1:J4')->applyFromArray($headerStyle);

// Set column headers
$headers = ['No', 'NIS', 'Nama Siswa', 'Rata-rata Tugas', 'UTS', 'UAS', 'Absensi', 'Rumus', 'Nilai Akhir', 'Status'];
$sheet->fromArray($headers, NULL, 'A6');

// Style column headers
$columnHeaderStyle = [
    'font' => ['bold' => true],
    'alignment' => [
        'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
    ],
    'fill' => [
        'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
        'startColor' => ['rgb' => 'E2EFDA']
    ]
];
$sheet->getStyle('A6:J6')->applyFromArray($columnHeaderStyle);

// Get nilai data
$nilai = new Nilai();
$result_nilai = $nilai->getNilaiMapel($mapel_id, $semester, $tahun_ajaran, $kelas_id);

// Add data
$row = 7;
$no = 1;
while($data = $result_nilai->fetch(PDO::FETCH_ASSOC)) {
    $nilai_akhir = $data['nilai_akhir'] ?? 0;
    $status = $nilai_akhir >= $mapel->kkm ? 'Tuntas' : 'Belum Tuntas';
    $color = $nilai_akhir >= $mapel->kkm ? '00FF00' : 'FF0000';

    $sheet->setCellValue('A'.$row, $no++);
    $sheet->setCellValue('B'.$row, $data['nis']);
    $sheet->setCellValue('C'.$row, $data['nama_siswa']);
    $sheet->setCellValue('D'.$row, $data['rata_tugas'] ? number_format($data['rata_tugas'], 2) : '-');
    $sheet->setCellValue('E'.$row, $data['nilai_uts'] ?? '-');
    $sheet->setCellValue('F'.$row, $data['nilai_uas'] ?? '-');
    $sheet->setCellValue('G'.$row, $data['nilai_absen'] ?? '-');
    $sheet->setCellValue('H'.$row, $data['rumus_nilai'] ?? '-');
    $sheet->setCellValue('I'.$row, $nilai_akhir ?: '-');
    $sheet->setCellValue('J'.$row, $status);

    // Color the nilai akhir and status cells based on KKM
    if ($nilai_akhir > 0) {
        $sheet->getStyle('I'.$row)->getFont()->getColor()->setRGB($color);
        $sheet->getStyle('J'.$row)->getFont()->getColor()->setRGB($color);
    }

    $row++;
}

// Auto-size columns
foreach(range('A','J') as $col) {
    $sheet->getColumnDimension($col)->setAutoSize(true);
}

// Set print area
$sheet->getPageSetup()->setPrintArea('A1:J'.($row-1));

// Center align numeric columns
$sheet->getStyle('A7:A'.($row-1))->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER);
$sheet->getStyle('D7:J'.($row-1))->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER);

// Set headers for Excel download
header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
header('Content-Disposition: attachment;filename="Nilai_' . $mapel->nama_mapel . '_' .
    ($kelas ? $kelas->nama_kelas . '_' : '') .
    'Semester_' . $semester . '_' .
    $tahun_ajaran . '.xlsx"');
header('Cache-Control: max-age=0');

$writer = new Xlsx($spreadsheet);
$writer->save('php://output');
