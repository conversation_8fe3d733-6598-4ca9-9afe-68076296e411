<?php
require_once __DIR__ . '/../middleware/auth.php';
checkGuruAccess();

require_once '../vendor/autoload.php';
require_once '../config/database.php';
require_once '../models/Rpp.php';
require_once '../models/RppKegiatan.php';
require_once '../models/MataPelajaran.php';
require_once '../models/Kelas.php';
require_once '../models/Guru.php';

use PhpOffice\PhpWord\PhpWord;
use PhpOffice\PhpWord\Style\Font;
use PhpOffice\PhpWord\SimpleType\Jc;

try {
    if (!isset($_GET['id'])) {
        throw new Exception("ID RPP tidak ditemukan.");
    }

    $id = $_GET['id'];
    $rpp = new Rpp();
    $rppKegiatan = new RppKegiatan();

    // Ambil data RPP
    $stmt = $rpp->getOne($id);
    $rpp_data = $stmt->fetch(PDO::FETCH_ASSOC);
    if (!$rpp_data) {
        throw new Exception("RPP tidak ditemukan.");
    }

    // Ambil data kegiatan pembelajaran
    $stmt_kegiatan = $rppKegiatan->getByRppId($id);
    $kegiatan = [
        'pendahuluan' => [],
        'inti' => [],
        'penutup' => []
    ];

    while ($row = $stmt_kegiatan->fetch(PDO::FETCH_ASSOC)) {
        $kegiatan[$row['jenis_kegiatan']][] = $row;
    }

    // Ambil data tambahan
    $mapel = new MataPelajaran();
    $kelas = new Kelas();
    $guru = new Guru();

    // Set ID dan ambil data
    $mapel->id = $rpp_data['mapel_id'];
    $mapel_data = $mapel->getOne();

    $kelas_data = $kelas->getById($rpp_data['kelas_id']);

    $guru->id = $rpp_data['guru_id'];
    $guru_data = $guru->getOneNew();

    // Cek jika data tidak ditemukan
    if (!$mapel_data || !$kelas_data || !$guru_data) {
        throw new Exception("Data tidak lengkap.");
    }

    // Get application info
    $app_name = "SIHADIR - Sistem Informasi Kehadiran Siswa";
    $app_version = "v2.18.0";

    // Create new Word Document
    $phpWord = new PhpWord();

    // Set document properties
    $properties = $phpWord->getDocInfo();
    $properties->setCreator($app_name . ' ' . $app_version);
    $properties->setTitle('RPP - ' . $mapel_data['nama_mapel']);
    $properties->setDescription('Rencana Pelaksanaan Pembelajaran');

    // Set default font
    $phpWord->setDefaultFontName('Times New Roman');
    $phpWord->setDefaultFontSize(12);

    // Add section
    $section = $phpWord->addSection();

    // Add header
    $section->addText($app_name . ' ' . $app_version, ['size' => 10, 'color' => '666666'], ['alignment' => Jc::CENTER]);
    $section->addText('RENCANA PELAKSANAAN PEMBELAJARAN (RPP)', ['bold' => true, 'size' => 16, 'color' => '007bff'], ['alignment' => Jc::CENTER]);
    $section->addTextBreak();

    // Add table for identitas
    $table = $section->addTable(['borderSize' => 0]);
    $table->addRow();
    $table->addCell(2500)->addText('Nama Sekolah');
    $table->addCell(500)->addText(':');
    $table->addCell(4000)->addText($rpp_data['nama_sekolah']);

    $table->addRow();
    $table->addCell(2500)->addText('Mata Pelajaran');
    $table->addCell(500)->addText(':');
    $table->addCell(4000)->addText($mapel_data['nama_mapel']);

    $table->addRow();
    $table->addCell(2500)->addText('Kelas');
    $table->addCell(500)->addText(':');
    $table->addCell(4000)->addText($kelas_data['nama_kelas']);

    $table->addRow();
    $table->addCell(2500)->addText('Semester');
    $table->addCell(500)->addText(':');
    $table->addCell(4000)->addText($rpp_data['semester']);

    $table->addRow();
    $table->addCell(2500)->addText('Tahun Ajaran');
    $table->addCell(500)->addText(':');
    $table->addCell(4000)->addText($rpp_data['tahun_ajaran']);

    $table->addRow();
    $table->addCell(2500)->addText('Alokasi Waktu');
    $table->addCell(500)->addText(':');
    $table->addCell(4000)->addText($rpp_data['alokasi_waktu']);

    $table->addRow();
    $table->addCell(2500)->addText('Guru');
    $table->addCell(500)->addText(':');
    $table->addCell(4000)->addText($guru_data['nama_lengkap']);

    $section->addTextBreak();

    // Add content sections
    $section->addText('A. Tujuan Pembelajaran', ['bold' => true]);
    $section->addText($rpp_data['tujuan_pembelajaran']);
    $section->addTextBreak();

    $section->addText('B. Kompetensi Dasar', ['bold' => true]);
    $section->addText($rpp_data['kompetensi_dasar']);
    $section->addTextBreak();

    $section->addText('C. Indikator Pencapaian Kompetensi', ['bold' => true]);
    $section->addText($rpp_data['indikator_pencapaian']);
    $section->addTextBreak();

    $section->addText('D. Materi Pembelajaran', ['bold' => true]);
    $section->addText($rpp_data['materi_pembelajaran']);
    $section->addTextBreak();

    $section->addText('E. Kegiatan Pembelajaran', ['bold' => true]);
    
    $section->addText('1. Pendahuluan', ['bold' => true]);
    if (isset($kegiatan['pendahuluan'])) {
        foreach ($kegiatan['pendahuluan'] as $item) {
            $section->addText($item['deskripsi'], null, ['indent' => 1]);
        }
    }
    $section->addTextBreak();

    $section->addText('2. Kegiatan Inti', ['bold' => true]);
    if (isset($kegiatan['inti'])) {
        foreach ($kegiatan['inti'] as $item) {
            $section->addText($item['deskripsi'], null, ['indent' => 1]);
        }
    }
    $section->addTextBreak();

    $section->addText('3. Penutup', ['bold' => true]);
    if (isset($kegiatan['penutup'])) {
        foreach ($kegiatan['penutup'] as $item) {
            $section->addText($item['deskripsi'], null, ['indent' => 1]);
        }
    }
    $section->addTextBreak();

    $section->addText('F. Metode Pembelajaran', ['bold' => true]);
    $section->addText($rpp_data['metode_pembelajaran']);
    $section->addTextBreak();

    $section->addText('G. Media Pembelajaran', ['bold' => true]);
    $section->addText($rpp_data['media_pembelajaran']);
    $section->addTextBreak();

    $section->addText('H. Sumber Belajar', ['bold' => true]);
    $section->addText($rpp_data['sumber_belajar']);
    $section->addTextBreak();

    $section->addText('I. Penilaian', ['bold' => true]);
    $section->addText($rpp_data['penilaian']);
    $section->addTextBreak(2);

    // Add footer with signature
    $section->addText($rpp_data['nama_sekolah'] . ', ' . date('d F Y'), null, ['alignment' => Jc::END]);
    $section->addText('Guru Mata Pelajaran', ['bold' => true], ['alignment' => Jc::END]);
    $section->addTextBreak(3);
    $section->addText($guru_data['nama_lengkap'], ['bold' => true], ['alignment' => Jc::END]);
    $section->addText('NIP: ' . ($guru_data['nip'] ?? '-'), null, ['alignment' => Jc::END]);

    // Save file
    $filename = "RPP_" . $mapel_data['nama_mapel'] . "_" . date('Ymd') . ".docx";
    header("Content-Description: File Transfer");
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Content-Type: application/vnd.openxmlformats-officedocument.wordprocessingml.document');
    header('Content-Transfer-Encoding: binary');
    header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
    header('Expires: 0');

    $objWriter = \PhpOffice\PhpWord\IOFactory::createWriter($phpWord, 'Word2007');
    $objWriter->save('php://output');
    exit();

} catch (Exception $e) {
    error_log('Word Export Error: ' . $e->getMessage());
    $_SESSION['error'] = "Maaf, terjadi kesalahan saat mengekspor file Word: " . $e->getMessage();
    header("Location: index.php");
    exit();
}