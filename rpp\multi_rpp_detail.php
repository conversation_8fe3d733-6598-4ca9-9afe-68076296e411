<?php
require_once __DIR__ . '/../middleware/auth.php';
checkGuruAccess();
require_once '../template/header.php';
require_once '../config/database.php';
require_once '../models/MultiRppExam.php';
require_once '../models/Rpp.php';
require_once '../models/Guru.php';

// Validasi parameter
if (!isset($_GET['exam_id'])) {
    $_SESSION['error'] = "ID ujian tidak ditemukan.";
    header("Location: multi_rpp_generate.php");
    exit();
}

$exam_id = $_GET['exam_id'];

// Dapatkan guru_id
$guru = new Guru();
$stmt = $guru->getByUserId($_SESSION['user_id']);
if ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
    $guru_id = $row['id'];
    $guru_data = $row;
} else {
    die("Data guru tidak ditemukan");
}

// Ambil data ujian multi-RPP
$multiRppExam = new MultiRppExam();
$stmt = $multiRppExam->getOne($exam_id);
$exam_data = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$exam_data || $exam_data['guru_id'] != $guru_id) {
    $_SESSION['error'] = "Ujian tidak ditemukan atau bukan milik Anda.";
    header("Location: multi_rpp_generate.php");
    exit();
}

// Decode JSON data
$selected_rpp_ids = json_decode($exam_data['selected_rpp_ids'], true);
$question_distribution = json_decode($exam_data['question_distribution'], true);

// Ambil data RPP yang terkait
$rpp = new Rpp();
$related_rpps = [];
foreach ($selected_rpp_ids as $rpp_id) {
    $stmt = $rpp->getOne($rpp_id);
    $rpp_data = $stmt->fetch(PDO::FETCH_ASSOC);
    if ($rpp_data) {
        $related_rpps[] = $rpp_data;
    }
}

// Ambil soal-soal ujian
$questions_stmt = $multiRppExam->getQuestionsByExamId($exam_id);
$questions = [];
while ($question = $questions_stmt->fetch(PDO::FETCH_ASSOC)) {
    $questions[] = $question;
}

// Ambil statistik soal
$stats_stmt = $multiRppExam->getQuestionStats($exam_id);
$question_stats = [];
while ($stat = $stats_stmt->fetch(PDO::FETCH_ASSOC)) {
    $question_stats[] = $stat;
}

$success_msg = isset($_SESSION['success']) ? $_SESSION['success'] : '';
$error_msg = isset($_SESSION['error']) ? $_SESSION['error'] : '';
unset($_SESSION['success'], $_SESSION['error']);
?>

<div class="container-fluid">
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">
                <i class="fas fa-layer-group"></i> Detail Ujian Multi-RPP
            </h5>
            <div>
                <a href="multi_blueprint_generate.php?exam_id=<?= $exam_id ?>" class="btn btn-info me-2">
                    <i class="fas fa-file-alt"></i> Generate Kisi-kisi
                </a>
                <a href="multi_rpp_generate.php" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Kembali
                </a>
            </div>
        </div>
        <div class="card-body">
            <?php if ($success_msg): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?= $success_msg ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <?php if ($error_msg): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?= $error_msg ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <div class="row">
                <div class="col-md-8">
                    <!-- Exam Information -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-info-circle"></i> Informasi Ujian</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td width="150"><strong>Judul Ujian:</strong></td>
                                            <td><?= htmlspecialchars($exam_data['exam_title']) ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Jenis Ujian:</strong></td>
                                            <td><?= htmlspecialchars($exam_data['exam_type']) ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Semester:</strong></td>
                                            <td><?= htmlspecialchars($exam_data['semester']) ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Tahun Ajaran:</strong></td>
                                            <td><?= htmlspecialchars($exam_data['tahun_ajaran']) ?></td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td width="150"><strong>Durasi:</strong></td>
                                            <td><?= htmlspecialchars($exam_data['exam_duration']) ?> menit</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Total Skor:</strong></td>
                                            <td><?= htmlspecialchars($exam_data['total_score']) ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Total Soal:</strong></td>
                                            <td><?= count($questions) ?> soal</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Dibuat:</strong></td>
                                            <td><?= date('d/m/Y H:i', strtotime($exam_data['created_at'])) ?></td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                            
                            <?php if (!empty($exam_data['additional_notes'])): ?>
                                <div class="mt-3">
                                    <strong>Catatan Tambahan:</strong>
                                    <p class="mt-2"><?= nl2br(htmlspecialchars($exam_data['additional_notes'])) ?></p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Related RPPs -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-layer-group"></i> RPP/Chapter Terkait (<?= count($related_rpps) ?> RPP)</h6>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>No</th>
                                            <th>Mata Pelajaran</th>
                                            <th>Kelas</th>
                                            <th>Tema/Chapter</th>
                                            <th>Materi Pokok</th>
                                            <th>Soal</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($related_rpps as $index => $rpp_data): ?>
                                            <?php
                                            $rpp_question_count = 0;
                                            foreach ($question_stats as $stat) {
                                                if ($stat['source_rpp_id'] == $rpp_data['id']) {
                                                    $rpp_question_count = $stat['total'];
                                                    break;
                                                }
                                            }
                                            ?>
                                            <tr>
                                                <td><?= $index + 1 ?></td>
                                                <td><?= htmlspecialchars($rpp_data['nama_mapel']) ?></td>
                                                <td><?= htmlspecialchars($rpp_data['nama_kelas']) ?></td>
                                                <td><strong><?= htmlspecialchars($rpp_data['tema_subtema']) ?></strong></td>
                                                <td><?= htmlspecialchars($rpp_data['materi_pokok']) ?></td>
                                                <td>
                                                    <span class="badge bg-info"><?= $rpp_question_count ?> soal</span>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Questions List -->
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h6 class="mb-0"><i class="fas fa-list"></i> Daftar Soal (<?= count($questions) ?> soal)</h6>
                            <div>
                                <a href="multi_rpp_export.php?exam_id=<?= $exam_id ?>&format=pdf" class="btn btn-sm btn-outline-danger" target="_blank">
                                    <i class="fas fa-file-pdf"></i> PDF
                                </a>
                                <a href="multi_rpp_export.php?exam_id=<?= $exam_id ?>&format=word" class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-file-word"></i> Word
                                </a>
                            </div>
                        </div>
                        <div class="card-body">
                            <?php if (empty($questions)): ?>
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle"></i>
                                    Belum ada soal yang tersimpan untuk ujian ini.
                                </div>
                            <?php else: ?>
                                <div class="accordion" id="questionsAccordion">
                                    <?php foreach ($questions as $index => $question): ?>
                                        <div class="accordion-item">
                                            <h2 class="accordion-header" id="heading<?= $index ?>">
                                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" 
                                                        data-bs-target="#collapse<?= $index ?>" aria-expanded="false" 
                                                        aria-controls="collapse<?= $index ?>">
                                                    <div class="d-flex justify-content-between align-items-center w-100 me-3">
                                                        <span>
                                                            <strong>Soal <?= $index + 1 ?></strong> - <?= htmlspecialchars($question['tema_subtema']) ?>
                                                        </span>
                                                        <div>
                                                            <span class="badge bg-primary me-1"><?= ucfirst(str_replace('_', ' ', $question['question_type'])) ?></span>
                                                            <span class="badge bg-secondary me-1"><?= ucfirst(str_replace('_', ' ', $question['difficulty_level'])) ?></span>
                                                            <span class="badge bg-info"><?= $question['cognitive_level'] ?></span>
                                                        </div>
                                                    </div>
                                                </button>
                                            </h2>
                                            <div id="collapse<?= $index ?>" class="accordion-collapse collapse" 
                                                 aria-labelledby="heading<?= $index ?>" data-bs-parent="#questionsAccordion">
                                                <div class="accordion-body">
                                                    <div class="mb-3">
                                                        <strong>Pertanyaan:</strong>
                                                        <p class="mt-2"><?= nl2br(htmlspecialchars($question['question_text'])) ?></p>
                                                    </div>
                                                    
                                                    <?php if ($question['question_type'] === 'multiple_choice' && !empty($question['options'])): ?>
                                                        <?php $options = json_decode($question['options'], true); ?>
                                                        <div class="mb-3">
                                                            <strong>Pilihan Jawaban:</strong>
                                                            <div class="mt-2">
                                                                <?php foreach ($options as $option): ?>
                                                                    <div class="mb-1">
                                                                        <?php if ($option === $question['correct_answer'] || 
                                                                                  (is_string($option) && substr($option, 0, 1) === $question['correct_answer'])): ?>
                                                                            <span class="text-success fw-bold"><?= htmlspecialchars($option) ?> ✓</span>
                                                                        <?php else: ?>
                                                                            <?= htmlspecialchars($option) ?>
                                                                        <?php endif; ?>
                                                                    </div>
                                                                <?php endforeach; ?>
                                                            </div>
                                                        </div>
                                                    <?php endif; ?>
                                                    
                                                    <div class="row">
                                                        <div class="col-md-6">
                                                            <small class="text-muted">
                                                                <strong>Source RPP:</strong> <?= htmlspecialchars($question['tema_subtema']) ?><br>
                                                                <strong>Materi:</strong> <?= htmlspecialchars($question['materi_pokok']) ?>
                                                            </small>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <small class="text-muted">
                                                                <strong>Mata Pelajaran:</strong> <?= htmlspecialchars($question['nama_mapel']) ?><br>
                                                                <strong>Kategori:</strong> <?= htmlspecialchars($question['category'] ?? 'Generated') ?>
                                                            </small>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <!-- Statistics -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="card-title mb-0"><i class="fas fa-chart-pie"></i> Statistik Soal</h6>
                        </div>
                        <div class="card-body">
                            <?php
                            $total_questions = count($questions);
                            $mc_count = 0;
                            $essay_count = 0;
                            $difficulty_stats = ['regular' => 0, 'hots_easy' => 0, 'hots_medium' => 0, 'hots_hard' => 0];
                            $cognitive_stats = ['C1' => 0, 'C2' => 0, 'C3' => 0, 'C4' => 0, 'C5' => 0, 'C6' => 0];
                            
                            foreach ($questions as $question) {
                                if ($question['question_type'] === 'multiple_choice') {
                                    $mc_count++;
                                } else {
                                    $essay_count++;
                                }
                                
                                if (isset($difficulty_stats[$question['difficulty_level']])) {
                                    $difficulty_stats[$question['difficulty_level']]++;
                                }
                                
                                if (isset($cognitive_stats[$question['cognitive_level']])) {
                                    $cognitive_stats[$question['cognitive_level']]++;
                                }
                            }
                            ?>
                            
                            <h6>Jenis Soal:</h6>
                            <div class="mb-3">
                                <div class="d-flex justify-content-between">
                                    <span>Pilihan Ganda:</span>
                                    <span><strong><?= $mc_count ?></strong></span>
                                </div>
                                <div class="d-flex justify-content-between">
                                    <span>Essay:</span>
                                    <span><strong><?= $essay_count ?></strong></span>
                                </div>
                            </div>
                            
                            <h6>Tingkat Kesulitan:</h6>
                            <div class="mb-3">
                                <div class="d-flex justify-content-between">
                                    <span>Regular:</span>
                                    <span><?= $difficulty_stats['regular'] ?></span>
                                </div>
                                <div class="d-flex justify-content-between">
                                    <span>HOTS Mudah:</span>
                                    <span><?= $difficulty_stats['hots_easy'] ?></span>
                                </div>
                                <div class="d-flex justify-content-between">
                                    <span>HOTS Sedang:</span>
                                    <span><?= $difficulty_stats['hots_medium'] ?></span>
                                </div>
                                <div class="d-flex justify-content-between">
                                    <span>HOTS Tinggi:</span>
                                    <span><?= $difficulty_stats['hots_hard'] ?></span>
                                </div>
                            </div>
                            
                            <h6>Level Kognitif:</h6>
                            <div class="small">
                                <?php foreach ($cognitive_stats as $level => $count): ?>
                                    <?php if ($count > 0): ?>
                                        <div class="d-flex justify-content-between">
                                            <span><?= $level ?>:</span>
                                            <span><?= $count ?></span>
                                        </div>
                                    <?php endif; ?>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>

                    <!-- Distribution by RPP -->
                    <div class="card">
                        <div class="card-header">
                            <h6 class="card-title mb-0"><i class="fas fa-balance-scale"></i> Distribusi per RPP</h6>
                        </div>
                        <div class="card-body">
                            <?php foreach ($question_stats as $stat): ?>
                                <div class="mb-3">
                                    <h6 class="mb-1"><?= htmlspecialchars($stat['tema_subtema']) ?></h6>
                                    <div class="small text-muted mb-2"><?= htmlspecialchars($stat['materi_pokok']) ?></div>
                                    <div class="d-flex justify-content-between">
                                        <span>Total Soal:</span>
                                        <span><strong><?= $stat['total'] ?></strong></span>
                                    </div>
                                    <div class="d-flex justify-content-between">
                                        <span>Pilihan Ganda:</span>
                                        <span><?= $stat['multiple_choice'] ?></span>
                                    </div>
                                    <div class="d-flex justify-content-between">
                                        <span>Essay:</span>
                                        <span><?= $stat['essay'] ?></span>
                                    </div>
                                    <div class="d-flex justify-content-between">
                                        <span>Regular:</span>
                                        <span><?= $stat['regular'] ?></span>
                                    </div>
                                    <div class="d-flex justify-content-between">
                                        <span>HOTS:</span>
                                        <span><?= $stat['hots_easy'] + $stat['hots_medium'] + $stat['hots_hard'] ?></span>
                                    </div>
                                </div>
                                <hr>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.table th {
    font-weight: 600;
    font-size: 0.9rem;
}

.table td {
    vertical-align: middle;
    font-size: 0.9rem;
}

.card-title {
    color: #495057;
}

.badge {
    font-size: 0.75rem;
}

.accordion-button:not(.collapsed) {
    background-color: #e7f1ff;
    color: #0c63e4;
}

.accordion-button:focus {
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}
</style>

<?php require_once '../template/footer.php'; ?>
