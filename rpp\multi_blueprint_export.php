<?php
require_once __DIR__ . '/../middleware/auth.php';
checkGuruAccess();
require_once '../vendor/autoload.php';
require_once '../config/database.php';
require_once '../models/ExamBlueprint.php';
require_once '../models/Guru.php';

use Dompdf\Dompdf;
use Dompdf\Options;
use PhpOffice\PhpWord\PhpWord;
use PhpOffice\PhpWord\IOFactory;
use PhpOffice\PhpWord\Style\Font;

// Validasi parameter
if (!isset($_GET['blueprint_id']) || !isset($_GET['format'])) {
    $_SESSION['error'] = "Parameter tidak lengkap.";
    header("Location: blueprint_list.php");
    exit();
}

$blueprint_id = $_GET['blueprint_id'];
$format = $_GET['format'];

if (!in_array($format, ['pdf', 'word'])) {
    $_SESSION['error'] = "Format export tidak valid.";
    header("Location: blueprint_list.php");
    exit();
}

// Dapatkan guru_id
$guru = new Guru();
$stmt = $guru->getByUserId($_SESSION['user_id']);
if ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
    $guru_id = $row['id'];
    $guru_data = $row;
} else {
    die("Data guru tidak ditemukan");
}

// Ambil data kisi-kisi
$examBlueprint = new ExamBlueprint();
$stmt = $examBlueprint->getOne($blueprint_id);
$blueprint_data = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$blueprint_data || $blueprint_data['guru_id'] != $guru_id) {
    $_SESSION['error'] = "Kisi-kisi tidak ditemukan atau bukan milik Anda.";
    header("Location: blueprint_list.php");
    exit();
}

// Decode JSON data
$generated_blueprint = json_decode($blueprint_data['blueprint_data'], true);

if (!$generated_blueprint) {
    $_SESSION['error'] = "Data kisi-kisi tidak valid.";
    header("Location: multi_blueprint_result.php?blueprint_id=" . $blueprint_id);
    exit();
}

// Get application info
$app_name = "SIHADIR - Sistem Informasi Kehadiran Siswa";
$app_version = "v2.18.0";

try {
    if ($format === 'pdf') {
        exportToPDF($blueprint_data, $generated_blueprint, $app_name, $app_version);
    } else {
        exportToWord($blueprint_data, $generated_blueprint, $app_name, $app_version);
    }
} catch (Exception $e) {
    $_SESSION['error'] = "Gagal export kisi-kisi: " . $e->getMessage();
    header("Location: multi_blueprint_result.php?blueprint_id=" . $blueprint_id);
    exit();
}

function exportToPDF($blueprint_data, $generated_blueprint, $app_name, $app_version) {
    $options = new Options();
    $options->set('defaultFont', 'Arial');
    $options->set('isRemoteEnabled', true);
    $options->set('isHtml5ParserEnabled', true);

    $dompdf = new Dompdf($options);
    
    $html = generateBlueprintHTML($blueprint_data, $generated_blueprint, $app_name, $app_version);
    
    $dompdf->loadHtml($html);
    $dompdf->setPaper('A4', 'portrait');
    $dompdf->render();
    
    $filename = 'Blueprint_' . str_replace(' ', '_', $blueprint_data['blueprint_title']) . '_' . date('Y-m-d') . '.pdf';
    $dompdf->stream($filename, array('Attachment' => true));
}

function exportToWord($blueprint_data, $generated_blueprint, $app_name, $app_version) {
    $phpWord = new PhpWord();
    
    // Set document properties
    $properties = $phpWord->getDocInfo();
    $properties->setCreator($app_name);
    $properties->setTitle('Blueprint: ' . $blueprint_data['blueprint_title']);
    $properties->setDescription('Generated by ' . $app_name . ' ' . $app_version);
    
    $section = $phpWord->addSection([
        'marginTop' => 1134,
        'marginBottom' => 1134,
        'marginLeft' => 1134,
        'marginRight' => 1134
    ]);
    
    // Header
    $headerStyle = ['bold' => true, 'size' => 16, 'name' => 'Arial'];
    $section->addText('KISI-KISI UJIAN', $headerStyle, ['alignment' => 'center']);
    $section->addText($blueprint_data['blueprint_title'], $headerStyle, ['alignment' => 'center']);
    $section->addTextBreak(2);
    
    // Exam info table
    $exam_info = $generated_blueprint['exam_info'] ?? [];
    $table = $section->addTable(['borderSize' => 6, 'borderColor' => '000000']);
    
    $table->addRow();
    $table->addCell(3000)->addText('Mata Pelajaran', ['bold' => true]);
    $table->addCell(3000)->addText($exam_info['subject'] ?? '');
    $table->addCell(3000)->addText('Jenis Ujian', ['bold' => true]);
    $table->addCell(3000)->addText($exam_info['type'] ?? '');
    
    $table->addRow();
    $table->addCell(3000)->addText('Durasi', ['bold' => true]);
    $table->addCell(3000)->addText($exam_info['duration'] ?? '');
    $table->addCell(3000)->addText('Total Skor', ['bold' => true]);
    $table->addCell(3000)->addText($exam_info['total_score'] ?? '');
    
    $section->addTextBreak(2);
    
    // Learning objectives
    if (!empty($generated_blueprint['learning_objectives'])) {
        $section->addText('TUJUAN PEMBELAJARAN', ['bold' => true, 'size' => 14]);
        $section->addTextBreak();
        
        foreach ($generated_blueprint['learning_objectives'] as $objective) {
            $section->addText($objective['chapter'] ?? '', ['bold' => true, 'size' => 12]);
            if (isset($objective['objectives']) && is_array($objective['objectives'])) {
                foreach ($objective['objectives'] as $obj) {
                    $section->addText('• ' . $obj, ['size' => 11]);
                }
            }
            $section->addTextBreak();
        }
    }
    
    // Blueprint table
    if (!empty($generated_blueprint['blueprint_table'])) {
        $section->addText('TABEL KISI-KISI', ['bold' => true, 'size' => 14]);
        $section->addTextBreak();
        
        $blueprintTable = $section->addTable(['borderSize' => 6, 'borderColor' => '000000']);
        
        // Header
        $blueprintTable->addRow();
        $blueprintTable->addCell(2000)->addText('Chapter', ['bold' => true]);
        $blueprintTable->addCell(3000)->addText('Tujuan Pembelajaran', ['bold' => true]);
        $blueprintTable->addCell(2500)->addText('Indikator', ['bold' => true]);
        $blueprintTable->addCell(1500)->addText('Level Kognitif', ['bold' => true]);
        $blueprintTable->addCell(1500)->addText('No. Soal', ['bold' => true]);
        $blueprintTable->addCell(1000)->addText('Jumlah', ['bold' => true]);
        
        // Data
        foreach ($generated_blueprint['blueprint_table'] as $row) {
            $blueprintTable->addRow();
            $blueprintTable->addCell(2000)->addText($row['chapter'] ?? '');
            $blueprintTable->addCell(3000)->addText($row['learning_objective'] ?? '');
            $blueprintTable->addCell(2500)->addText($row['indicator'] ?? '');
            $blueprintTable->addCell(1500)->addText($row['cognitive_level'] ?? '');
            $blueprintTable->addCell(1500)->addText(
                isset($row['question_numbers']) && is_array($row['question_numbers']) 
                ? implode(', ', $row['question_numbers']) 
                : ''
            );
            $blueprintTable->addCell(1000)->addText($row['total_questions'] ?? '0');
        }
    }
    
    $section->addTextBreak(2);
    
    // Footer
    $section->addText('Generated by ' . $app_name . ' ' . $app_version . ' on ' . date('d/m/Y H:i'), 
                     ['size' => 8, 'name' => 'Arial', 'color' => '666666'], 
                     ['alignment' => 'center']);
    
    $filename = 'Blueprint_' . str_replace(' ', '_', $blueprint_data['blueprint_title']) . '_' . date('Y-m-d') . '.docx';
    
    header('Content-Type: application/vnd.openxmlformats-officedocument.wordprocessingml.document');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Cache-Control: max-age=0');
    
    $writer = IOFactory::createWriter($phpWord, 'Word2007');
    $writer->save('php://output');
    exit();
}

function generateBlueprintHTML($blueprint_data, $generated_blueprint, $app_name, $app_version) {
    $exam_info = $generated_blueprint['exam_info'] ?? [];
    
    $html = '<!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>Blueprint: ' . htmlspecialchars($blueprint_data['blueprint_title']) . '</title>
        <style>
            body { 
                font-family: Arial, sans-serif; 
                font-size: 12px; 
                line-height: 1.4; 
                margin: 20px;
            }
            .header { 
                text-align: center; 
                margin-bottom: 30px; 
                border-bottom: 2px solid #333;
                padding-bottom: 15px;
            }
            .blueprint-title { 
                font-size: 18px; 
                font-weight: bold; 
                margin-bottom: 10px; 
            }
            .exam-info { 
                margin-bottom: 25px; 
                background-color: #f8f9fa;
                padding: 15px;
                border-radius: 5px;
            }
            .exam-info table {
                width: 100%;
                border-collapse: collapse;
                border: 1px solid #ddd;
            }
            .exam-info td {
                padding: 8px;
                border: 1px solid #ddd;
            }
            .exam-info td:nth-child(odd) {
                background-color: #f8f9fa;
                font-weight: bold;
            }
            .section-title {
                font-size: 14px;
                font-weight: bold;
                margin: 25px 0 15px 0;
                color: #333;
                border-bottom: 1px solid #ddd;
                padding-bottom: 5px;
            }
            .objectives-list {
                margin-bottom: 20px;
            }
            .chapter-title {
                font-weight: bold;
                margin: 15px 0 5px 0;
                color: #495057;
            }
            .objective-item {
                margin-left: 20px;
                margin-bottom: 5px;
            }
            .blueprint-table {
                width: 100%;
                border-collapse: collapse;
                margin-bottom: 25px;
            }
            .blueprint-table th,
            .blueprint-table td {
                border: 1px solid #333;
                padding: 8px;
                text-align: left;
                vertical-align: top;
            }
            .blueprint-table th {
                background-color: #333;
                color: white;
                font-weight: bold;
                text-align: center;
            }
            .text-center {
                text-align: center;
            }
            .cognitive-table {
                width: 60%;
                margin: 0 auto 25px auto;
                border-collapse: collapse;
            }
            .cognitive-table th,
            .cognitive-table td {
                border: 1px solid #ddd;
                padding: 8px;
                text-align: center;
            }
            .cognitive-table th {
                background-color: #f8f9fa;
                font-weight: bold;
            }
            .footer { 
                margin-top: 40px; 
                text-align: center; 
                font-size: 10px; 
                color: #666; 
                border-top: 1px solid #ddd;
                padding-top: 15px;
            }
        </style>
    </head>
    <body>
        <div class="header">
            <div class="blueprint-title">KISI-KISI UJIAN</div>
            <div class="blueprint-title">' . htmlspecialchars($blueprint_data['blueprint_title']) . '</div>
        </div>
        
        <div class="exam-info">
            <table>
                <tr>
                    <td width="150">Mata Pelajaran</td>
                    <td>' . htmlspecialchars($exam_info['subject'] ?? '') . '</td>
                    <td width="150">Jenis Ujian</td>
                    <td>' . htmlspecialchars($exam_info['type'] ?? '') . '</td>
                </tr>
                <tr>
                    <td>Durasi</td>
                    <td>' . htmlspecialchars($exam_info['duration'] ?? '') . '</td>
                    <td>Total Skor</td>
                    <td>' . htmlspecialchars($exam_info['total_score'] ?? '') . '</td>
                </tr>
            </table>
        </div>';
    
    // Learning objectives
    if (!empty($generated_blueprint['learning_objectives'])) {
        $html .= '<div class="section-title">TUJUAN PEMBELAJARAN</div>
        <div class="objectives-list">';
        
        foreach ($generated_blueprint['learning_objectives'] as $objective) {
            $html .= '<div class="chapter-title">' . htmlspecialchars($objective['chapter'] ?? '') . '</div>';
            if (isset($objective['objectives']) && is_array($objective['objectives'])) {
                foreach ($objective['objectives'] as $obj) {
                    $html .= '<div class="objective-item">• ' . htmlspecialchars($obj) . '</div>';
                }
            }
        }
        
        $html .= '</div>';
    }
    
    // Cognitive mapping
    if (!empty($generated_blueprint['cognitive_mapping'])) {
        $html .= '<div class="section-title">PEMETAAN LEVEL KOGNITIF</div>
        <table class="cognitive-table">
            <tr>
                <th>Level</th>
                <th>Jumlah Soal</th>
                <th>Persentase</th>
            </tr>';
        
        $total_questions = array_sum($generated_blueprint['cognitive_mapping']);
        foreach ($generated_blueprint['cognitive_mapping'] as $level => $count) {
            if ($count > 0) {
                $percentage = $total_questions > 0 ? round(($count / $total_questions) * 100, 1) : 0;
                $html .= '<tr>
                    <td>' . $level . '</td>
                    <td>' . $count . '</td>
                    <td>' . $percentage . '%</td>
                </tr>';
            }
        }
        
        $html .= '</table>';
    }
    
    // Blueprint table
    if (!empty($generated_blueprint['blueprint_table'])) {
        $html .= '<div class="section-title">TABEL KISI-KISI</div>
        <table class="blueprint-table">
            <tr>
                <th width="15%">Chapter</th>
                <th width="25%">Tujuan Pembelajaran</th>
                <th width="25%">Indikator</th>
                <th width="12%">Level Kognitif</th>
                <th width="13%">No. Soal</th>
                <th width="10%">Jumlah</th>
            </tr>';
        
        foreach ($generated_blueprint['blueprint_table'] as $row) {
            $html .= '<tr>
                <td>' . htmlspecialchars($row['chapter'] ?? '') . '</td>
                <td>' . htmlspecialchars($row['learning_objective'] ?? '') . '</td>
                <td>' . htmlspecialchars($row['indicator'] ?? '') . '</td>
                <td class="text-center">' . htmlspecialchars($row['cognitive_level'] ?? '') . '</td>
                <td class="text-center">' . (
                    isset($row['question_numbers']) && is_array($row['question_numbers']) 
                    ? implode(', ', $row['question_numbers']) 
                    : ''
                ) . '</td>
                <td class="text-center">' . ($row['total_questions'] ?? '0') . '</td>
            </tr>';
        }
        
        $html .= '</table>';
    }
    
    $html .= '<div class="footer">
            Generated by ' . $app_name . ' ' . $app_version . ' on ' . date('d/m/Y H:i') . '
        </div>
    </body>
    </html>';
    
    return $html;
}
?>
