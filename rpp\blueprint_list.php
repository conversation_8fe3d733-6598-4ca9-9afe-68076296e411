<?php
require_once __DIR__ . '/../middleware/auth.php';
checkGuruAccess();
require_once '../template/header.php';
require_once '../config/database.php';
require_once '../models/ExamBlueprint.php';
require_once '../models/Guru.php';

// Dapatkan guru_id
$guru = new Guru();
$stmt = $guru->getByUserId($_SESSION['user_id']);
if ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
    $guru_id = $row['id'];
    $guru_data = $row;
} else {
    die("Data guru tidak ditemukan");
}

$examBlueprint = new ExamBlueprint();

// Handle delete request
if (isset($_GET['delete']) && isset($_GET['id'])) {
    $blueprint_id = $_GET['id'];
    
    // Verify ownership
    $stmt = $examBlueprint->getOne($blueprint_id);
    $blueprint_data = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($blueprint_data && $blueprint_data['guru_id'] == $guru_id) {
        if ($examBlueprint->delete($blueprint_id)) {
            $_SESSION['success'] = "Kisi-kisi berhasil dihapus.";
        } else {
            $_SESSION['error'] = "Gagal menghapus kisi-kisi.";
        }
    } else {
        $_SESSION['error'] = "Kisi-kisi tidak ditemukan atau bukan milik Anda.";
    }
    
    header("Location: blueprint_list.php");
    exit();
}

// Handle search
$search_term = isset($_GET['search']) ? trim($_GET['search']) : '';
if (!empty($search_term)) {
    $blueprints_stmt = $examBlueprint->searchBlueprints($search_term, $guru_id);
} else {
    $blueprints_stmt = $examBlueprint->getAllByGuru($guru_id);
}

// Get statistics
$stats = $examBlueprint->getBlueprintStats($guru_id);

$success_msg = isset($_SESSION['success']) ? $_SESSION['success'] : '';
$error_msg = isset($_SESSION['error']) ? $_SESSION['error'] : '';
unset($_SESSION['success'], $_SESSION['error']);
?>

<div class="container-fluid">
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">
                <i class="fas fa-file-alt"></i> Daftar Kisi-kisi Ujian
            </h5>
            <div>
                <a href="multi_rpp_list.php" class="btn btn-outline-primary">
                    <i class="fas fa-layer-group"></i> Ujian Multi-RPP
                </a>
                <a href="index.php" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Kembali ke RPP
                </a>
            </div>
        </div>
        <div class="card-body">
            <?php if ($success_msg): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?= $success_msg ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <?php if ($error_msg): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?= $error_msg ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <!-- Statistics -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body text-center">
                            <h4><?= $stats['total_blueprints'] ?? 0 ?></h4>
                            <p class="mb-0">Total Kisi-kisi</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body text-center">
                            <h4><?= $stats['multi_rpp_blueprints'] ?? 0 ?></h4>
                            <p class="mb-0">Multi-RPP</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body text-center">
                            <h4><?= $stats['single_rpp_blueprints'] ?? 0 ?></h4>
                            <p class="mb-0">Single RPP</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body text-center">
                            <h4><?= date('m/Y') ?></h4>
                            <p class="mb-0">Bulan Ini</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Search -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <form method="GET" class="d-flex">
                        <input type="text" class="form-control" name="search" 
                               placeholder="Cari kisi-kisi..." value="<?= htmlspecialchars($search_term) ?>">
                        <button type="submit" class="btn btn-outline-primary ms-2">
                            <i class="fas fa-search"></i>
                        </button>
                        <?php if (!empty($search_term)): ?>
                            <a href="blueprint_list.php" class="btn btn-outline-secondary ms-2">
                                <i class="fas fa-times"></i>
                            </a>
                        <?php endif; ?>
                    </form>
                </div>
            </div>

            <?php if ($blueprints_stmt && $blueprints_stmt->rowCount() > 0): ?>
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>No</th>
                                <th>Judul Kisi-kisi</th>
                                <th>Jenis</th>
                                <th>Ujian Terkait</th>
                                <th>Dibuat</th>
                                <th>Aksi</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php 
                            $no = 1;
                            while ($blueprint = $blueprints_stmt->fetch(PDO::FETCH_ASSOC)): 
                            ?>
                                <tr>
                                    <td><?= $no++ ?></td>
                                    <td>
                                        <strong><?= htmlspecialchars($blueprint['blueprint_title']) ?></strong>
                                    </td>
                                    <td>
                                        <?php if ($blueprint['exam_scope'] === 'Multi-RPP'): ?>
                                            <span class="badge bg-success">Multi-RPP</span>
                                        <?php else: ?>
                                            <span class="badge bg-info">Single RPP</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($blueprint['multi_exam_id']): ?>
                                            <a href="multi_rpp_detail.php?exam_id=<?= $blueprint['multi_exam_id'] ?>" 
                                               class="text-decoration-none">
                                                <i class="fas fa-layer-group"></i> Multi-RPP Exam
                                            </a>
                                        <?php elseif ($blueprint['rpp_id']): ?>
                                            <a href="view.php?id=<?= $blueprint['rpp_id'] ?>"
                                               class="text-decoration-none">
                                                <i class="fas fa-file-alt"></i> Single RPP
                                            </a>
                                        <?php else: ?>
                                            <span class="text-muted">-</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <small><?= date('d/m/Y H:i', strtotime($blueprint['created_at'])) ?></small>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <?php if ($blueprint['exam_scope'] === 'Multi-RPP'): ?>
                                                <a href="multi_blueprint_result.php?blueprint_id=<?= $blueprint['id'] ?>" 
                                                   class="btn btn-sm btn-outline-primary" title="Detail">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="multi_blueprint_export.php?blueprint_id=<?= $blueprint['id'] ?>&format=pdf" 
                                                   class="btn btn-sm btn-outline-danger" title="Export PDF" target="_blank">
                                                    <i class="fas fa-file-pdf"></i>
                                                </a>
                                                <a href="multi_blueprint_export.php?blueprint_id=<?= $blueprint['id'] ?>&format=word" 
                                                   class="btn btn-sm btn-outline-primary" title="Export Word">
                                                    <i class="fas fa-file-word"></i>
                                                </a>
                                            <?php else: ?>
                                                <a href="single_blueprint_result.php?blueprint_id=<?= $blueprint['id'] ?>"
                                                   class="btn btn-sm btn-outline-primary" title="Detail">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="single_blueprint_export.php?blueprint_id=<?= $blueprint['id'] ?>&format=pdf"
                                                   class="btn btn-sm btn-outline-danger" title="Export PDF" target="_blank">
                                                    <i class="fas fa-file-pdf"></i>
                                                </a>
                                                <a href="single_blueprint_export.php?blueprint_id=<?= $blueprint['id'] ?>&format=word"
                                                   class="btn btn-sm btn-outline-primary" title="Export Word">
                                                    <i class="fas fa-file-word"></i>
                                                </a>
                                            <?php endif; ?>
                                            
                                            <button type="button" class="btn btn-sm btn-outline-danger" 
                                                    onclick="confirmDelete(<?= $blueprint['id'] ?>, '<?= htmlspecialchars($blueprint['blueprint_title']) ?>')" 
                                                    title="Hapus">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            <?php endwhile; ?>
                        </tbody>
                    </table>
                </div>
            <?php else: ?>
                <div class="text-center py-5">
                    <div class="mb-3">
                        <i class="fas fa-file-alt fa-3x text-muted"></i>
                    </div>
                    <?php if (!empty($search_term)): ?>
                        <h5 class="text-muted">Tidak Ada Hasil Pencarian</h5>
                        <p class="text-muted mb-4">
                            Tidak ditemukan kisi-kisi dengan kata kunci "<?= htmlspecialchars($search_term) ?>".
                        </p>
                        <a href="blueprint_list.php" class="btn btn-outline-primary">
                            <i class="fas fa-arrow-left"></i> Kembali ke Semua Kisi-kisi
                        </a>
                    <?php else: ?>
                        <h5 class="text-muted">Belum Ada Kisi-kisi</h5>
                        <p class="text-muted mb-4">
                            Anda belum membuat kisi-kisi ujian. Kisi-kisi dapat dibuat dari ujian multi-RPP 
                            atau RPP individual yang sudah memiliki soal.
                        </p>
                        <div class="d-flex justify-content-center gap-2">
                            <a href="multi_rpp_generate.php" class="btn btn-success">
                                <i class="fas fa-layer-group"></i> Buat Ujian Multi-RPP
                            </a>
                            <a href="generate_questions.php" class="btn btn-primary">
                                <i class="fas fa-magic"></i> Generate Soal RPP
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">
                    <i class="fas fa-exclamation-triangle text-warning"></i> Konfirmasi Hapus
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Apakah Anda yakin ingin menghapus kisi-kisi:</p>
                <p><strong id="blueprintTitle"></strong></p>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>Peringatan:</strong> Tindakan ini tidak dapat dibatalkan.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                <a href="#" id="deleteConfirmBtn" class="btn btn-danger">
                    <i class="fas fa-trash"></i> Ya, Hapus
                </a>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDelete(blueprintId, blueprintTitle) {
    document.getElementById('blueprintTitle').textContent = blueprintTitle;
    document.getElementById('deleteConfirmBtn').href = 'blueprint_list.php?delete=1&id=' + blueprintId;
    
    const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
    deleteModal.show();
}

// Auto-hide alerts after 5 seconds
document.addEventListener('DOMContentLoaded', function() {
    const alerts = document.querySelectorAll('.alert-dismissible');
    alerts.forEach(function(alert) {
        setTimeout(function() {
            const bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        }, 5000);
    });
});
</script>

<style>
.table th {
    font-weight: 600;
    font-size: 0.9rem;
}

.table td {
    vertical-align: middle;
    font-size: 0.9rem;
}

.btn-group .btn {
    margin-right: 2px;
}

.btn-group .btn:last-child {
    margin-right: 0;
}

.badge {
    font-size: 0.75rem;
}

.card-title {
    color: #495057;
}

.fa-3x {
    font-size: 3rem;
}

.card h4 {
    font-size: 2rem;
    font-weight: bold;
}
</style>

<?php require_once '../template/footer.php'; ?>
