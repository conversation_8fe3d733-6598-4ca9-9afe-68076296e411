<?php
require_once __DIR__ . '/../middleware/auth.php';
checkGuruAccess();
require_once '../config/database.php';
require_once '../models/MultiRppExam.php';
require_once '../models/ExamBlueprint.php';
require_once '../models/Guru.php';

// Cek jika bukan POST request
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    $_SESSION['error'] = "Akses tidak valid.";
    header("Location: multi_rpp_list.php");
    exit();
}

// Validasi input
if (!isset($_POST['exam_id']) || !isset($_POST['blueprint_data'])) {
    $_SESSION['error'] = "Data tidak lengkap.";
    header("Location: multi_rpp_list.php");
    exit();
}

$exam_id = $_POST['exam_id'];
$blueprint_data = json_decode($_POST['blueprint_data'], true);

if (!$blueprint_data) {
    $_SESSION['error'] = "Data kisi-kisi tidak valid.";
    header("Location: multi_blueprint_generate.php?exam_id=" . $exam_id);
    exit();
}

// Dapatkan guru_id
$guru = new Guru();
$stmt = $guru->getByUserId($_SESSION['user_id']);
if ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
    $guru_id = $row['id'];
    $guru_data = $row;
} else {
    die("Data guru tidak ditemukan");
}

// Validasi ujian multi-RPP
$multiRppExam = new MultiRppExam();
$stmt = $multiRppExam->getOne($exam_id);
$exam_data = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$exam_data || $exam_data['guru_id'] != $guru_id) {
    $_SESSION['error'] = "Ujian tidak ditemukan atau bukan milik Anda.";
    header("Location: multi_rpp_list.php");
    exit();
}

try {
    $database = new Database();
    $conn = $database->getConnection();
    $conn->beginTransaction();
    
    // Check if blueprint already exists for this exam
    $examBlueprint = new ExamBlueprint();
    $existing_stmt = $examBlueprint->getByMultiExamId($exam_id);
    $existing_blueprint = $existing_stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($existing_blueprint) {
        // Update existing blueprint
        $examBlueprint->id = $existing_blueprint['id'];
        $examBlueprint->guru_id = $guru_id;
        $examBlueprint->multi_exam_id = $exam_id;
        $examBlueprint->rpp_id = null; // Multi-RPP blueprint
        $examBlueprint->blueprint_title = $blueprint_data['exam_info']['title'] ?? $exam_data['exam_title'] . ' - Kisi-kisi';
        $examBlueprint->exam_info = json_encode($blueprint_data['exam_info'] ?? []);
        $examBlueprint->learning_objectives = json_encode($blueprint_data['learning_objectives'] ?? []);
        $examBlueprint->question_distribution = json_encode($blueprint_data['question_distribution'] ?? []);
        $examBlueprint->cognitive_mapping = json_encode($blueprint_data['cognitive_mapping'] ?? []);
        $examBlueprint->difficulty_distribution = json_encode($blueprint_data['summary']['difficulty_distribution'] ?? []);
        $examBlueprint->blueprint_data = json_encode($blueprint_data);
        
        if ($examBlueprint->update()) {
            $blueprint_id = $existing_blueprint['id'];
            $_SESSION['success'] = "Kisi-kisi ujian multi-RPP berhasil diperbarui.";
        } else {
            throw new Exception("Gagal memperbarui kisi-kisi ujian.");
        }
    } else {
        // Create new blueprint
        $examBlueprint->guru_id = $guru_id;
        $examBlueprint->multi_exam_id = $exam_id;
        $examBlueprint->rpp_id = null; // Multi-RPP blueprint
        $examBlueprint->blueprint_title = $blueprint_data['exam_info']['title'] ?? $exam_data['exam_title'] . ' - Kisi-kisi';
        $examBlueprint->exam_info = json_encode($blueprint_data['exam_info'] ?? []);
        $examBlueprint->learning_objectives = json_encode($blueprint_data['learning_objectives'] ?? []);
        $examBlueprint->question_distribution = json_encode($blueprint_data['question_distribution'] ?? []);
        $examBlueprint->cognitive_mapping = json_encode($blueprint_data['cognitive_mapping'] ?? []);
        $examBlueprint->difficulty_distribution = json_encode($blueprint_data['summary']['difficulty_distribution'] ?? []);
        $examBlueprint->blueprint_data = json_encode($blueprint_data);
        
        $blueprint_id = $examBlueprint->create();
        
        if (!$blueprint_id) {
            throw new Exception("Gagal menyimpan kisi-kisi ujian.");
        }
        
        $_SESSION['success'] = "Kisi-kisi ujian multi-RPP berhasil disimpan.";
    }
    
    $conn->commit();
    
    // Redirect to blueprint result page
    header("Location: multi_blueprint_result.php?blueprint_id=" . $blueprint_id);
    exit();
    
} catch (Exception $e) {
    $conn->rollBack();
    $_SESSION['error'] = "Gagal menyimpan kisi-kisi: " . $e->getMessage();
    
    // Redirect back to generate page
    header("Location: multi_blueprint_generate.php?exam_id=" . $exam_id);
    exit();
}
?>
