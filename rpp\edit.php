<?php
session_start();
require_once '../config/database.php';
require_once '../models/Rpp.php';
require_once '../models/RppKegiatan.php';
require_once '../models/MataPelajaran.php';
require_once '../models/Guru.php';
require_once '../models/Kelas.php';
require_once '../models/TahunAjaran.php';

// Cek role
if($_SESSION['role'] != 'guru') {
    header('Location: ../index.php');
    exit();
}

// Dapatkan guru_id dari tabel guru berdasarkan user_id yang login
$guru = new Guru();
$stmt = $guru->getByUserId($_SESSION['user_id']);
if ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
    $guru_id = $row['id'];
} else {
    die("Data guru tidak ditemukan");
}

$mapel = new MataPelajaran();
$kelas = new Kelas();
$tahunAjaran = new TahunAjaran();
$rpp = new Rpp();
$rppKegiatan = new RppKegiatan();

// Ambil data RPP yang akan diedit
if (isset($_GET['id'])) {
    $rpp_id = $_GET['id'];
    $stmt = $rpp->getOne($rpp_id);
    $rpp_data = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // Cek apakah RPP milik guru yang login
    if ($rpp_data['guru_id'] != $guru_id) {
        header('Location: index.php');
        exit();
    }
    
    // Ambil data kegiatan pembelajaran
    $kegiatan_list = $rppKegiatan->getByRppId($rpp_id);
} else {
    header('Location: index.php');
    exit();
}

// Ambil data untuk dropdown
$mapel_list = $mapel->getMapelByGuruId($guru_id);
$kelas_list = $kelas->getAll();
$tahun_ajaran_list = $tahunAjaran->getAll();

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // Update data RPP
    $rpp->id = $rpp_id;
    $rpp->mapel_id = $_POST['mapel_id'];
    $rpp->guru_id = $guru_id;
    $rpp->kelas_id = $_POST['kelas_id'];
    $rpp->semester = $_POST['semester'];
    $rpp->tahun_ajaran = $_POST['tahun_ajaran'];
    $rpp->nama_sekolah = $_POST['nama_sekolah'];
    $rpp->tema_subtema = $_POST['tema_subtema'];
    $rpp->materi_pokok = $_POST['materi_pokok'];
    $rpp->alokasi_waktu = $_POST['alokasi_waktu'];
    $rpp->tujuan_pembelajaran = $_POST['tujuan_pembelajaran'];
    $rpp->kompetensi_dasar = $_POST['kompetensi_dasar'];
    $rpp->indikator_pencapaian = $_POST['indikator_pencapaian'];
    $rpp->materi_pembelajaran = $_POST['materi_pembelajaran'];
    $rpp->metode_pembelajaran = $_POST['metode_pembelajaran'];
    $rpp->media_pembelajaran = $_POST['media_pembelajaran'];
    $rpp->sumber_belajar = $_POST['sumber_belajar'];
    $rpp->penilaian = $_POST['penilaian'];
    
    if ($rpp->update()) {
        // Hapus kegiatan pembelajaran lama
        $rppKegiatan->deleteByRppId($rpp_id);
        
        // Simpan kegiatan pembelajaran baru
        foreach ($_POST['pendahuluan'] as $index => $deskripsi) {
            if (!empty($deskripsi)) {
                $rppKegiatan->rpp_id = $rpp_id;
                $rppKegiatan->jenis_kegiatan = 'pendahuluan';
                $rppKegiatan->deskripsi = $deskripsi;
                $rppKegiatan->urutan = $index + 1;
                $rppKegiatan->create();
            }
        }

        foreach ($_POST['kegiatan_inti'] as $index => $deskripsi) {
            if (!empty($deskripsi)) {
                $rppKegiatan->rpp_id = $rpp_id;
                $rppKegiatan->jenis_kegiatan = 'inti';
                $rppKegiatan->deskripsi = $deskripsi;
                $rppKegiatan->urutan = $index + 1;
                $rppKegiatan->create();
            }
        }

        foreach ($_POST['penutup'] as $index => $deskripsi) {
            if (!empty($deskripsi)) {
                $rppKegiatan->rpp_id = $rpp_id;
                $rppKegiatan->jenis_kegiatan = 'penutup';
                $rppKegiatan->deskripsi = $deskripsi;
                $rppKegiatan->urutan = $index + 1;
                $rppKegiatan->create();
            }
        }
        
        header('Location: index.php');
        exit();
    }
}

// Ambil data kegiatan untuk ditampilkan di form
$kegiatan_pendahuluan = [];
$kegiatan_inti = [];
$kegiatan_penutup = [];

while ($row = $kegiatan_list->fetch(PDO::FETCH_ASSOC)) {
    switch ($row['jenis_kegiatan']) {
        case 'pendahuluan':
            $kegiatan_pendahuluan[] = $row;
            break;
        case 'inti':
            $kegiatan_inti[] = $row;
            break;
        case 'penutup':
            $kegiatan_penutup[] = $row;
            break;
    }
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Edit RPP</title>
    <?php include '../template/header.php'; ?>
</head>
<body>
    <div class="container mt-5">
        <h2>Edit RPP</h2>
        <form method="POST" action="">
            <div class="mb-3">
                <label for="kelas_id" class="form-label">Kelas</label>
                <select class="form-select" id="kelas_id" name="kelas_id" required>
                    <option value="">Pilih Kelas</option>
                    <?php
                    while ($row = $kelas_list->fetch(PDO::FETCH_ASSOC)):
                        $selected = ($row['id'] == $rpp_data['kelas_id']) ? 'selected' : '';
                    ?>
                        <option value="<?= $row['id'] ?>" <?= $selected ?>><?= $row['nama_kelas'] ?></option>
                    <?php endwhile; ?>
                </select>
                <small class="text-muted">Pilih kelas terlebih dahulu untuk melihat mata pelajaran yang tersedia</small>
            </div>

            <div class="mb-3">
                <label for="mapel_id" class="form-label">Mata Pelajaran</label>
                <select class="form-select" id="mapel_id" name="mapel_id" required>
                    <option value="">Pilih kelas terlebih dahulu</option>
                </select>
                <small class="text-muted">Mata pelajaran akan muncul setelah memilih kelas</small>
            </div>

            <div class="mb-3">
                <label for="semester" class="form-label">Semester</label>
                <select class="form-select" id="semester" name="semester" required>
                    <option value="">Pilih Semester</option>
                    <option value="1" <?= ($rpp_data['semester'] == '1') ? 'selected' : '' ?>>Semester 1</option>
                    <option value="2" <?= ($rpp_data['semester'] == '2') ? 'selected' : '' ?>>Semester 2</option>
                </select>
            </div>

            <div class="mb-3">
                <label for="tahun_ajaran" class="form-label">Tahun Ajaran</label>
                <select class="form-select" id="tahun_ajaran" name="tahun_ajaran" required>
                    <option value="">Pilih Tahun Ajaran</option>
                    <?php 
                    while ($row = $tahun_ajaran_list->fetch(PDO::FETCH_ASSOC)): 
                        $selected = ($row['tahun_ajaran'] == $rpp_data['tahun_ajaran']) ? 'selected' : '';
                    ?>
                        <option value="<?= $row['tahun_ajaran'] ?>" <?= $selected ?>><?= $row['tahun_ajaran'] ?></option>
                    <?php endwhile; ?>
                </select>
            </div>

            <div class="mb-3">
                <label for="nama_sekolah" class="form-label">Nama Sekolah</label>
                <input type="text" class="form-control" id="nama_sekolah" name="nama_sekolah" value="<?= $rpp_data['nama_sekolah'] ?>" required>
            </div>

            <div class="mb-3">
                <label for="tema_subtema" class="form-label">Tema/Subtema</label>
                <input type="text" class="form-control" id="tema_subtema" name="tema_subtema" value="<?= $rpp_data['tema_subtema'] ?>">
            </div>

            <div class="mb-3">
                <label for="materi_pokok" class="form-label">Materi Pokok</label>
                <input type="text" class="form-control" id="materi_pokok" name="materi_pokok" value="<?= $rpp_data['materi_pokok'] ?>" required>
            </div>

            <div class="mb-3">
                <label for="alokasi_waktu" class="form-label">Alokasi Waktu</label>
                <input type="text" class="form-control" id="alokasi_waktu" name="alokasi_waktu" value="<?= $rpp_data['alokasi_waktu'] ?>" required>
            </div>

            <div class="mb-3">
                <label for="tujuan_pembelajaran" class="form-label">Tujuan Pembelajaran</label>
                <textarea class="form-control" id="tujuan_pembelajaran" name="tujuan_pembelajaran" rows="3" required><?= $rpp_data['tujuan_pembelajaran'] ?></textarea>
            </div>

            <div class="mb-3">
                <label for="kompetensi_dasar" class="form-label">Kompetensi Dasar</label>
                <textarea class="form-control" id="kompetensi_dasar" name="kompetensi_dasar" rows="3" required><?= $rpp_data['kompetensi_dasar'] ?></textarea>
            </div>

            <div class="mb-3">
                <label for="indikator_pencapaian" class="form-label">Indikator Pencapaian</label>
                <textarea class="form-control" id="indikator_pencapaian" name="indikator_pencapaian" rows="3" required><?= $rpp_data['indikator_pencapaian'] ?></textarea>
            </div>

            <div class="mb-3">
                <label for="materi_pembelajaran" class="form-label">Materi Pembelajaran</label>
                <textarea class="form-control" id="materi_pembelajaran" name="materi_pembelajaran" rows="3" required><?= $rpp_data['materi_pembelajaran'] ?></textarea>
            </div>

            <div class="mb-3">
                <label for="metode_pembelajaran" class="form-label">Metode Pembelajaran</label>
                <textarea class="form-control" id="metode_pembelajaran" name="metode_pembelajaran" rows="3" required><?= $rpp_data['metode_pembelajaran'] ?></textarea>
            </div>

            <div class="mb-3">
                <label for="media_pembelajaran" class="form-label">Media Pembelajaran</label>
                <textarea class="form-control" id="media_pembelajaran" name="media_pembelajaran" rows="3" required><?= $rpp_data['media_pembelajaran'] ?></textarea>
            </div>

            <div class="mb-3">
                <label for="sumber_belajar" class="form-label">Sumber Belajar</label>
                <textarea class="form-control" id="sumber_belajar" name="sumber_belajar" rows="3" required><?= $rpp_data['sumber_belajar'] ?></textarea>
            </div>

            <div class="mb-3">
                <label for="penilaian" class="form-label">Penilaian</label>
                <textarea class="form-control" id="penilaian" name="penilaian" rows="3" required><?= $rpp_data['penilaian'] ?></textarea>
            </div>

            <div class="mb-3">
                <label class="form-label">Kegiatan Pembelajaran</label>
                <div id="kegiatan-container">
                    <?php foreach ($kegiatan_pendahuluan as $index => $kegiatan): ?>
                    <div class="kegiatan-item mb-3">
                        <div class="card">
                            <div class="card-body">
                                <h5 class="card-title">Kegiatan <?= $index + 1 ?></h5>
                                
                                <div class="mb-3">
                                    <label class="form-label">Pendahuluan</label>
                                    <textarea class="form-control" name="pendahuluan[]" rows="3" required><?= $kegiatan['deskripsi'] ?></textarea>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">Kegiatan Inti</label>
                                    <textarea class="form-control" name="kegiatan_inti[]" rows="3" required><?= isset($kegiatan_inti[$index]) ? $kegiatan_inti[$index]['deskripsi'] : '' ?></textarea>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">Penutup</label>
                                    <textarea class="form-control" name="penutup[]" rows="3" required><?= isset($kegiatan_penutup[$index]) ? $kegiatan_penutup[$index]['deskripsi'] : '' ?></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
                <button type="button" class="btn btn-secondary" onclick="tambahKegiatan()">Tambah Kegiatan</button>
            </div>

            <button type="submit" class="btn btn-primary">Simpan Perubahan</button>
            <a href="index.php" class="btn btn-secondary">Kembali</a>
        </form>
    </div>

    <script>
    let kegiatanCounter = <?= count($kegiatan_pendahuluan) ?>;

    // Function to load subjects based on selected class
    function loadMapelOptions() {
        const kelasId = document.getElementById('kelas_id').value;
        const mapelSelect = document.getElementById('mapel_id');
        const currentMapelId = '<?= $rpp_data['mapel_id'] ?>';

        // Clear existing options
        mapelSelect.innerHTML = '<option value="">Pilih mata pelajaran</option>';

        if (kelasId) {
            // Create AJAX request to get subjects for selected class
            fetch('get_mapel_by_kelas.php?kelas_id=' + kelasId)
                .then(response => response.json())
                .then(data => {
                    if (data.length === 0) {
                        mapelSelect.innerHTML = '<option value="">Tidak ada mata pelajaran tersedia</option>';
                    } else {
                        data.forEach(mapel => {
                            const option = document.createElement('option');
                            option.value = mapel.id;
                            option.textContent = mapel.nama_mapel;

                            // Pre-select the current subject if it matches
                            if (mapel.id == currentMapelId) {
                                option.selected = true;
                            }

                            mapelSelect.appendChild(option);
                        });
                    }
                })
                .catch(error => {
                    console.error('Error loading subjects:', error);
                    mapelSelect.innerHTML = '<option value="">Error loading mata pelajaran</option>';
                });
        }
    }

    // Event handler for class selection change
    document.getElementById('kelas_id').addEventListener('change', loadMapelOptions);

    // Load subjects for the initially selected class on page load
    document.addEventListener('DOMContentLoaded', function() {
        if (document.getElementById('kelas_id').value) {
            loadMapelOptions();
        }
    });

    function tambahKegiatan() {
        kegiatanCounter++;
        const container = document.getElementById('kegiatan-container');

        const kegiatanDiv = document.createElement('div');
        kegiatanDiv.className = 'kegiatan-item mb-3';
        kegiatanDiv.innerHTML = `
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Kegiatan ${kegiatanCounter}</h5>

                    <div class="mb-3">
                        <label class="form-label">Pendahuluan</label>
                        <textarea class="form-control" name="pendahuluan[]" rows="3" required></textarea>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Kegiatan Inti</label>
                        <textarea class="form-control" name="kegiatan_inti[]" rows="3" required></textarea>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Penutup</label>
                        <textarea class="form-control" name="penutup[]" rows="3" required></textarea>
                    </div>
                </div>
            </div>
        `;

        container.appendChild(kegiatanDiv);
    }
    </script>
</body>
</html>