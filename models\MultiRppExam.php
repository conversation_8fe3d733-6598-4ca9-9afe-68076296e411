<?php
require_once __DIR__ . '/../config/database.php';

class MultiRppExam {
    private $conn;
    private $table_name = "multi_rpp_exams";
    private $questions_table = "multi_rpp_exam_questions";

    public $id;
    public $guru_id;
    public $exam_title;
    public $exam_type;
    public $semester;
    public $tahun_ajaran;
    public $exam_duration;
    public $total_score;
    public $selected_rpp_ids;
    public $question_distribution;
    public $additional_notes;

    public function __construct() {
        $database = new Database();
        $this->conn = $database->getConnection();
    }

    public function create() {
        $query = "INSERT INTO " . $this->table_name . " SET
                guru_id=:guru_id, exam_title=:exam_title, exam_type=:exam_type,
                semester=:semester, tahun_ajaran=:tahun_ajaran, exam_duration=:exam_duration,
                total_score=:total_score, selected_rpp_ids=:selected_rpp_ids,
                question_distribution=:question_distribution, additional_notes=:additional_notes";

        $stmt = $this->conn->prepare($query);

        // Bind
        $stmt->bindParam(":guru_id", $this->guru_id);
        $stmt->bindParam(":exam_title", $this->exam_title);
        $stmt->bindParam(":exam_type", $this->exam_type);
        $stmt->bindParam(":semester", $this->semester);
        $stmt->bindParam(":tahun_ajaran", $this->tahun_ajaran);
        $stmt->bindParam(":exam_duration", $this->exam_duration);
        $stmt->bindParam(":total_score", $this->total_score);
        $stmt->bindParam(":selected_rpp_ids", $this->selected_rpp_ids);
        $stmt->bindParam(":question_distribution", $this->question_distribution);
        $stmt->bindParam(":additional_notes", $this->additional_notes);

        if($stmt->execute()) {
            return $this->conn->lastInsertId();
        }
        return false;
    }

    public function getAll() {
        $query = "SELECT me.*, g.nama_lengkap as nama_guru 
                 FROM " . $this->table_name . " me
                 LEFT JOIN guru g ON me.guru_id = g.id
                 ORDER BY me.created_at DESC";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        return $stmt;
    }

    public function getAllByGuru($guru_id) {
        $query = "SELECT me.*, g.nama_lengkap as nama_guru 
                FROM " . $this->table_name . " me
                LEFT JOIN guru g ON me.guru_id = g.id
                WHERE me.guru_id = :guru_id
                ORDER BY me.created_at DESC";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':guru_id', $guru_id);
        $stmt->execute();
        
        return $stmt;
    }

    public function getOne($id) {
        $query = "SELECT me.*, g.nama_lengkap as nama_guru 
                 FROM " . $this->table_name . " me
                 LEFT JOIN guru g ON me.guru_id = g.id
                 WHERE me.id = ?";
        $stmt = $this->conn->prepare($query);
        $stmt->execute([$id]);
        return $stmt;
    }

    public function update() {
        $query = "UPDATE " . $this->table_name . " SET
                guru_id=:guru_id, exam_title=:exam_title, exam_type=:exam_type,
                semester=:semester, tahun_ajaran=:tahun_ajaran, exam_duration=:exam_duration,
                total_score=:total_score, selected_rpp_ids=:selected_rpp_ids,
                question_distribution=:question_distribution, additional_notes=:additional_notes
                WHERE id=:id";
    
        $stmt = $this->conn->prepare($query);

        // Bind
        $stmt->bindParam(":guru_id", $this->guru_id);
        $stmt->bindParam(":exam_title", $this->exam_title);
        $stmt->bindParam(":exam_type", $this->exam_type);
        $stmt->bindParam(":semester", $this->semester);
        $stmt->bindParam(":tahun_ajaran", $this->tahun_ajaran);
        $stmt->bindParam(":exam_duration", $this->exam_duration);
        $stmt->bindParam(":total_score", $this->total_score);
        $stmt->bindParam(":selected_rpp_ids", $this->selected_rpp_ids);
        $stmt->bindParam(":question_distribution", $this->question_distribution);
        $stmt->bindParam(":additional_notes", $this->additional_notes);
        $stmt->bindParam(":id", $this->id);

        return $stmt->execute();
    }

    public function delete($id) {
        try {
            $this->conn->beginTransaction();

            // Delete questions first (foreign key constraint)
            $query = "DELETE FROM " . $this->questions_table . " WHERE multi_exam_id = :id";
            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(':id', $id);
            $stmt->execute();

            // Delete the exam
            $query = "DELETE FROM " . $this->table_name . " WHERE id = :id";
            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(':id', $id);
            $result = $stmt->execute();

            $this->conn->commit();
            return $result;

        } catch (Exception $e) {
            $this->conn->rollBack();
            return false;
        }
    }

    public function getSelectedRppData($multi_exam_id) {
        try {
            // First, get the selected RPP IDs from the exam
            $exam_query = "SELECT selected_rpp_ids FROM " . $this->table_name . " WHERE id = :multi_exam_id";
            $exam_stmt = $this->conn->prepare($exam_query);
            $exam_stmt->bindParam(':multi_exam_id', $multi_exam_id);
            $exam_stmt->execute();
            $exam_data = $exam_stmt->fetch(PDO::FETCH_ASSOC);

            if (!$exam_data || empty($exam_data['selected_rpp_ids'])) {
                // Return empty result set if no exam found or no RPPs selected
                return $this->getEmptyRppResultSet();
            }

            // Decode the JSON array of selected RPP IDs
            $selected_rpp_ids = json_decode($exam_data['selected_rpp_ids'], true);

            if (!is_array($selected_rpp_ids) || empty($selected_rpp_ids)) {
                // Return empty result set if JSON decode failed or array is empty
                return $this->getEmptyRppResultSet();
            }

            // Sanitize the IDs to ensure they are integers (security measure)
            $selected_rpp_ids = array_map('intval', array_filter($selected_rpp_ids, 'is_numeric'));

            if (empty($selected_rpp_ids)) {
                return $this->getEmptyRppResultSet();
            }

            // Create placeholders for the IN clause
            $placeholders = str_repeat('?,', count($selected_rpp_ids) - 1) . '?';

            // Build the query using IN clause instead of JSON functions
            $query = "SELECT ? as selected_rpp_ids, r.*, m.nama_mapel, k.nama_kelas
                     FROM rpp r
                     LEFT JOIN mata_pelajaran m ON r.mapel_id = m.id
                     LEFT JOIN kelas k ON r.kelas_id = k.id
                     WHERE r.id IN ($placeholders)
                     ORDER BY r.tema_subtema";

            $stmt = $this->conn->prepare($query);

            // Bind the original JSON string as first parameter, then the RPP IDs
            $params = array_merge([$exam_data['selected_rpp_ids']], $selected_rpp_ids);
            $stmt->execute($params);

            return $stmt;

        } catch (Exception $e) {
            // Log the error for debugging
            error_log("Error in getSelectedRppData: " . $e->getMessage());

            // Return empty result set on any error
            return $this->getEmptyRppResultSet();
        }
    }

    private function getEmptyRppResultSet() {
        // Return empty result set with correct structure
        $empty_query = "SELECT
                          NULL as selected_rpp_ids,
                          NULL as id,
                          NULL as guru_id,
                          NULL as mapel_id,
                          NULL as kelas_id,
                          NULL as tema_subtema,
                          NULL as materi_pokok,
                          NULL as tujuan_pembelajaran,
                          NULL as kompetensi_dasar,
                          NULL as indikator_pencapaian,
                          NULL as semester,
                          NULL as tahun_ajaran,
                          NULL as created_at,
                          NULL as updated_at,
                          NULL as nama_mapel,
                          NULL as nama_kelas
                        WHERE 1=0"; // This will return empty result set with correct structure
        $stmt = $this->conn->prepare($empty_query);
        $stmt->execute();
        return $stmt;
    }

    public function getQuestionsByExamId($multi_exam_id) {
        $query = "SELECT meq.*, r.tema_subtema, r.materi_pokok, m.nama_mapel
                 FROM " . $this->questions_table . " meq
                 LEFT JOIN rpp r ON meq.source_rpp_id = r.id
                 LEFT JOIN mata_pelajaran m ON r.mapel_id = m.id
                 WHERE meq.multi_exam_id = :multi_exam_id
                 ORDER BY meq.source_rpp_id, meq.question_type, meq.id";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':multi_exam_id', $multi_exam_id);
        $stmt->execute();
        
        return $stmt;
    }

    public function getQuestionStats($multi_exam_id) {
        $query = "SELECT 
                    COUNT(*) as total,
                    SUM(CASE WHEN question_type = 'multiple_choice' THEN 1 ELSE 0 END) as multiple_choice,
                    SUM(CASE WHEN question_type = 'essay' THEN 1 ELSE 0 END) as essay,
                    SUM(CASE WHEN difficulty_level = 'regular' THEN 1 ELSE 0 END) as regular,
                    SUM(CASE WHEN difficulty_level = 'hots_easy' THEN 1 ELSE 0 END) as hots_easy,
                    SUM(CASE WHEN difficulty_level = 'hots_medium' THEN 1 ELSE 0 END) as hots_medium,
                    SUM(CASE WHEN difficulty_level = 'hots_hard' THEN 1 ELSE 0 END) as hots_hard,
                    source_rpp_id,
                    r.tema_subtema,
                    r.materi_pokok
                 FROM " . $this->questions_table . " meq
                 LEFT JOIN rpp r ON meq.source_rpp_id = r.id
                 WHERE meq.multi_exam_id = :multi_exam_id
                 GROUP BY meq.source_rpp_id
                 ORDER BY r.tema_subtema";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':multi_exam_id', $multi_exam_id);
        $stmt->execute();
        
        return $stmt;
    }

    public function saveQuestions($multi_exam_id, $questions_data) {
        try {
            $this->conn->beginTransaction();

            // Delete existing questions for this exam
            $delete_query = "DELETE FROM " . $this->questions_table . " WHERE multi_exam_id = :multi_exam_id";
            $delete_stmt = $this->conn->prepare($delete_query);
            $delete_stmt->bindParam(":multi_exam_id", $multi_exam_id);
            $delete_stmt->execute();

            // Insert new questions
            $query = "INSERT INTO " . $this->questions_table . " SET
                    multi_exam_id=:multi_exam_id, source_rpp_id=:source_rpp_id,
                    question_text=:question_text, question_type=:question_type,
                    options=:options, correct_answer=:correct_answer,
                    difficulty_level=:difficulty_level, cognitive_level=:cognitive_level,
                    category=:category, source_type=:source_type";

            $stmt = $this->conn->prepare($query);
            $created_ids = [];

            foreach ($questions_data as $question) {
                $multi_exam_id_val = $multi_exam_id;
                $source_rpp_id = $question['source_rpp_id'];
                $question_text = $question['question_text'];
                $question_type = $question['question_type'];
                $options = isset($question['options']) ? json_encode($question['options']) : null;
                $correct_answer = $question['correct_answer'] ?? null;
                $difficulty_level = $question['difficulty_level'] ?? 'regular';
                $cognitive_level = $question['cognitive_level'] ?? null;
                $category = $question['category'] ?? 'Generated';
                $source_type = $question['source_type'] ?? 'generated';

                // Bind
                $stmt->bindParam(":multi_exam_id", $multi_exam_id_val);
                $stmt->bindParam(":source_rpp_id", $source_rpp_id);
                $stmt->bindParam(":question_text", $question_text);
                $stmt->bindParam(":question_type", $question_type);
                $stmt->bindParam(":options", $options);
                $stmt->bindParam(":correct_answer", $correct_answer);
                $stmt->bindParam(":difficulty_level", $difficulty_level);
                $stmt->bindParam(":cognitive_level", $cognitive_level);
                $stmt->bindParam(":category", $category);
                $stmt->bindParam(":source_type", $source_type);

                if ($stmt->execute()) {
                    $created_ids[] = $this->conn->lastInsertId();
                } else {
                    throw new Exception("Gagal menyimpan soal: " . $question_text);
                }
            }

            $this->conn->commit();
            return $created_ids;

        } catch (Exception $e) {
            $this->conn->rollBack();
            throw $e;
        }
    }
}
?>
