<?php
require_once __DIR__ . '/../config/database.php';

class RppQuestion {
    private $conn;
    private $table_name = "rpp_questions";

    public $id;
    public $rpp_id;
    public $question_text;
    public $question_type;
    public $options;
    public $correct_answer;
    public $difficulty_level;
    public $category;
    public $source_type;
    public $analysis_data;

    public function __construct() {
        $database = new Database();
        $this->conn = $database->getConnection();
    }

    public function create() {
        $query = "INSERT INTO " . $this->table_name . " SET
                rpp_id=:rpp_id, question_text=:question_text, question_type=:question_type,
                options=:options, correct_answer=:correct_answer, difficulty_level=:difficulty_level,
                category=:category, source_type=:source_type, analysis_data=:analysis_data";

        $stmt = $this->conn->prepare($query);

        // Sanitize
        $this->rpp_id = htmlspecialchars(strip_tags($this->rpp_id));
        $this->question_text = htmlspecialchars(strip_tags($this->question_text));
        $this->question_type = htmlspecialchars(strip_tags($this->question_type));
        $this->correct_answer = htmlspecialchars(strip_tags($this->correct_answer));
        $this->difficulty_level = htmlspecialchars(strip_tags($this->difficulty_level));
        $this->category = htmlspecialchars(strip_tags($this->category));
        $this->source_type = htmlspecialchars(strip_tags($this->source_type));

        // Convert options array to JSON string if it's an array
        $options_json = null;
        if ($this->options !== null) {
            if (is_array($this->options)) {
                $options_json = json_encode($this->options);
            } else if (is_string($this->options)) {
                // Validate string input - must be valid JSON or convert to null
                $trimmed = trim($this->options);
                if (empty($trimmed)) {
                    $options_json = null;
                } else {
                    // Test if it's valid JSON
                    json_decode($trimmed);
                    if (json_last_error() === JSON_ERROR_NONE) {
                        $options_json = $trimmed;
                    } else {
                        $options_json = null;
                    }
                }
            } else {
                $options_json = null;
            }
        }

        // Convert analysis_data array to JSON string if it's an array
        $analysis_data_json = null;
        if ($this->analysis_data !== null) {
            if (is_array($this->analysis_data)) {
                $analysis_data_json = json_encode($this->analysis_data);
            } else {
                $analysis_data_json = $this->analysis_data;
            }
        }

        // Bind
        $stmt->bindParam(":rpp_id", $this->rpp_id);
        $stmt->bindParam(":question_text", $this->question_text);
        $stmt->bindParam(":question_type", $this->question_type);
        $stmt->bindParam(":options", $options_json);
        $stmt->bindParam(":correct_answer", $this->correct_answer);
        $stmt->bindParam(":difficulty_level", $this->difficulty_level);
        $stmt->bindParam(":category", $this->category);
        $stmt->bindParam(":source_type", $this->source_type);
        $stmt->bindParam(":analysis_data", $analysis_data_json);

        if($stmt->execute()) {
            return $this->conn->lastInsertId();
        }
        return false;
    }

    public function getByRppId($rpp_id) {
        $query = "SELECT * FROM " . $this->table_name . "
                 WHERE rpp_id = :rpp_id
                 ORDER BY created_at DESC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":rpp_id", $rpp_id);
        $stmt->execute();

        return $stmt;
    }

    public function getByRppIdWithFilter($rpp_id, $filter = 'all') {
        $query = "SELECT * FROM " . $this->table_name . "
                 WHERE rpp_id = :rpp_id";

        if ($filter !== 'all') {
            $query .= " AND question_type = :question_type";
        }

        $query .= " ORDER BY created_at DESC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":rpp_id", $rpp_id);

        if ($filter !== 'all') {
            $stmt->bindParam(":question_type", $filter);
        }

        $stmt->execute();

        return $stmt;
    }

    public function getOne($id) {
        $query = "SELECT * FROM " . $this->table_name . " WHERE id = :id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":id", $id);
        $stmt->execute();
        
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    public function update() {
        $query = "UPDATE " . $this->table_name . " SET
                question_text=:question_text, question_type=:question_type,
                options=:options, correct_answer=:correct_answer, 
                difficulty_level=:difficulty_level, category=:category
                WHERE id=:id";

        $stmt = $this->conn->prepare($query);

        // Sanitize
        $this->question_text = htmlspecialchars(strip_tags($this->question_text));
        $this->question_type = htmlspecialchars(strip_tags($this->question_type));
        $this->correct_answer = htmlspecialchars(strip_tags($this->correct_answer));
        $this->difficulty_level = htmlspecialchars(strip_tags($this->difficulty_level));
        $this->category = htmlspecialchars(strip_tags($this->category));
        $this->id = htmlspecialchars(strip_tags($this->id));

        // Convert options array to JSON string if it's an array
        $options_json = null;
        if ($this->options !== null) {
            if (is_array($this->options)) {
                $options_json = json_encode($this->options);
            } else if (is_string($this->options)) {
                // Validate string input - must be valid JSON or convert to null
                $trimmed = trim($this->options);
                if (empty($trimmed)) {
                    $options_json = null;
                } else {
                    // Test if it's valid JSON
                    json_decode($trimmed);
                    if (json_last_error() === JSON_ERROR_NONE) {
                        $options_json = $trimmed;
                    } else {
                        $options_json = null;
                    }
                }
            } else {
                $options_json = null;
            }
        }

        // Bind
        $stmt->bindParam(":question_text", $this->question_text);
        $stmt->bindParam(":question_type", $this->question_type);
        $stmt->bindParam(":options", $options_json);
        $stmt->bindParam(":correct_answer", $this->correct_answer);
        $stmt->bindParam(":difficulty_level", $this->difficulty_level);
        $stmt->bindParam(":category", $this->category);
        $stmt->bindParam(":id", $this->id);

        return $stmt->execute();
    }

    public function delete($id) {
        $query = "DELETE FROM " . $this->table_name . " WHERE id = :id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":id", $id);
        
        return $stmt->execute();
    }

    public function deleteByRppId($rpp_id) {
        $query = "DELETE FROM " . $this->table_name . " WHERE rpp_id = :rpp_id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":rpp_id", $rpp_id);
        
        return $stmt->execute();
    }

    public function getStatsByRppId($rpp_id) {
        $query = "SELECT 
                    question_type,
                    difficulty_level,
                    COUNT(*) as count
                  FROM " . $this->table_name . " 
                  WHERE rpp_id = :rpp_id 
                  GROUP BY question_type, difficulty_level";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":rpp_id", $rpp_id);
        $stmt->execute();
        
        return $stmt;
    }

    public function getCountByRppId($rpp_id) {
        $query = "SELECT COUNT(*) as total FROM " . $this->table_name . " WHERE rpp_id = :rpp_id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":rpp_id", $rpp_id);
        $stmt->execute();
        
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result['total'];
    }

    public function createBulk($questions_data) {
        try {
            $this->conn->beginTransaction();

            $query = "INSERT INTO " . $this->table_name . " SET
                    rpp_id=:rpp_id, question_text=:question_text, question_type=:question_type,
                    options=:options, correct_answer=:correct_answer, difficulty_level=:difficulty_level,
                    category=:category, source_type=:source_type, analysis_data=:analysis_data";

            $stmt = $this->conn->prepare($query);
            $created_ids = [];

            foreach ($questions_data as $question) {
                // Sanitize
                $rpp_id = htmlspecialchars(strip_tags($question['rpp_id']));
                $question_text = htmlspecialchars(strip_tags($question['question_text']));
                $question_type = htmlspecialchars(strip_tags($question['question_type']));
                $correct_answer = isset($question['correct_answer']) ? htmlspecialchars(strip_tags($question['correct_answer'])) : null;
                $difficulty_level = htmlspecialchars(strip_tags($question['difficulty_level']));
                $category = htmlspecialchars(strip_tags($question['category']));
                $source_type = htmlspecialchars(strip_tags($question['source_type']));
                $options = isset($question['options']) ? $question['options'] : null;
                $analysis_data = isset($question['analysis_data']) ? $question['analysis_data'] : null;

                // Convert options array to JSON string if it's an array
                $options_json = null;
                if ($options !== null) {
                    if (is_array($options)) {
                        $options_json = json_encode($options);
                    } else if (is_string($options)) {
                        // Validate string input - must be valid JSON or convert to null
                        $trimmed = trim($options);
                        if (empty($trimmed)) {
                            $options_json = null;
                        } else {
                            // Test if it's valid JSON
                            json_decode($trimmed);
                            if (json_last_error() === JSON_ERROR_NONE) {
                                $options_json = $trimmed;
                            } else {
                                $options_json = null;
                            }
                        }
                    } else {
                        $options_json = null;
                    }
                }

                // Convert analysis_data array to JSON string if it's an array
                $analysis_data_json = null;
                if ($analysis_data !== null) {
                    if (is_array($analysis_data)) {
                        $analysis_data_json = json_encode($analysis_data);
                    } else {
                        $analysis_data_json = $analysis_data;
                    }
                }

                // Bind
                $stmt->bindParam(":rpp_id", $rpp_id);
                $stmt->bindParam(":question_text", $question_text);
                $stmt->bindParam(":question_type", $question_type);
                $stmt->bindParam(":options", $options_json);
                $stmt->bindParam(":correct_answer", $correct_answer);
                $stmt->bindParam(":difficulty_level", $difficulty_level);
                $stmt->bindParam(":category", $category);
                $stmt->bindParam(":source_type", $source_type);
                $stmt->bindParam(":analysis_data", $analysis_data_json);

                if ($stmt->execute()) {
                    $created_ids[] = $this->conn->lastInsertId();
                } else {
                    throw new Exception("Gagal menyimpan soal: " . $question_text);
                }
            }

            $this->conn->commit();
            return $created_ids;

        } catch (Exception $e) {
            $this->conn->rollBack();
            throw $e;
        }
    }

    public function deleteAllByRppId($rpp_id) {
        try {
            $query = "DELETE FROM " . $this->table_name . " WHERE rpp_id = :rpp_id";
            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(":rpp_id", $rpp_id);

            if ($stmt->execute()) {
                return $stmt->rowCount();
            }
            return 0;
        } catch (Exception $e) {
            error_log("Delete all questions error: " . $e->getMessage());
            return 0;
        }
    }
}
?>
