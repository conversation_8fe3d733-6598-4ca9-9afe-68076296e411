<?php
// First line: turn off output buffering
ob_end_clean();

// Start new output buffering
ob_start();

require_once '../vendor/autoload.php';
require_once '../config/database.php';
require_once '../models/MataPelajaran.php';
require_once '../models/Nilai.php';
require_once '../models/JadwalPelajaran.php';
require_once '../models/User.php';
session_start();

use Dompdf\Dompdf;
use Dompdf\Options;

try {
    if(!isset($_GET['mapel_id']) || !isset($_GET['semester']) || !isset($_GET['tahun_ajaran'])) {
        die("Parameter tidak lengkap.");
    }

    $mapel_id = $_GET['mapel_id'];
    $semester = $_GET['semester'];
    $tahun_ajaran = $_GET['tahun_ajaran'];
    $kelas_id = isset($_GET['kelas_id']) ? $_GET['kelas_id'] : null;

    // Check if user has access to this subject
    if (isset($_SESSION['role']) && $_SESSION['role'] == 'guru') {
        $user = new User();
        $guru_id = $user->getGuruId($_SESSION['user_id']);

        if ($guru_id) {
            $jadwal = new JadwalPelajaran();
            $query = "SELECT COUNT(*) as total FROM jadwal_pelajaran WHERE mapel_id = :mapel_id AND guru_id = :guru_id";
            $database = new Database();
            $conn = $database->getConnection();
            $stmt = $conn->prepare($query);
            $stmt->bindParam(":mapel_id", $mapel_id);
            $stmt->bindParam(":guru_id", $guru_id);
            $stmt->execute();
            $row = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($row['total'] == 0) {
                die("Anda tidak memiliki akses ke mata pelajaran ini.");
            }
        }
    }

    $mapel = new MataPelajaran();
    $mapel->id = $mapel_id;
    $mapel->getOne();

    $nilai = new Nilai();
    $result_nilai = $nilai->getNilaiMapel($mapel_id, $semester, $tahun_ajaran, $kelas_id);

    // Start generating PDF
    $options = new Options();
    $options->set('isHtml5ParserEnabled', true);
    $options->set('isPhpEnabled', true);
    $options->set('defaultFont', 'DejaVu Sans');

    $dompdf = new Dompdf($options);
    $dompdf->setPaper('A4', 'landscape');

    // Generate HTML content
    $html = '
    <html>
    <head>
        <style>
            body {
                font-family: Arial, sans-serif;
                font-size: 12px;
            }
            h2, h3 {
                text-align: center;
                margin: 5px 0;
            }
            table {
                width: 100%;
                border-collapse: collapse;
                margin-top: 10px;
            }
            th, td {
                border: 1px solid #000;
                padding: 5px;
                text-align: center;
            }
            th {
                background-color: #f2f2f2;
            }
            .header {
                text-align: center;
                margin-bottom: 20px;
            }
            .success {
                color: #008000;
            }
            .danger {
                color: #FF0000;
            }
            .kkm-info {
                text-align: center;
                margin: 10px 0;
                font-weight: bold;
                font-size: 14px;
            }
        </style>
    </head>
    <body>
        <div class="header">
            <p style="margin-bottom: 5px; font-size: 14px;"><strong>SIHADIR - Sistem Informasi Kehadiran Siswa | Versi 2.18.0</strong></p>
            <h2>Data Nilai '.$mapel->nama_mapel.'</h2>
            <h3>Semester '.$semester.' - Tahun Ajaran '.$tahun_ajaran.'</h3>
        </div>

        <div class="kkm-info">
            KKM (Kriteria Ketuntasan Minimal): '.$mapel->kkm.'
        </div>

        <table>
            <thead>
                <tr>
                    <th>No</th>
                    <th>NIS</th>
                    <th>Nama Siswa</th>
                    <th>Rata-rata Tugas</th>
                    <th>UTS</th>
                    <th>UAS</th>
                    <th>Absensi</th>
                    <th>Rumus</th>
                    <th>Nilai Akhir</th>
                    <th>Status</th>
                </tr>
            </thead>
            <tbody>';

    $no = 1;
    while($row = $result_nilai->fetch(PDO::FETCH_ASSOC)) {
        $nilai_akhir = $row['nilai_akhir'] ?? 0;
        $status = $nilai_akhir >= $mapel->kkm ? 'Tuntas' : 'Belum Tuntas';
        $color_class = $nilai_akhir >= $mapel->kkm ? 'success' : 'danger';

        $html .= '
            <tr>
                <td>'.$no++.'</td>
                <td>'.$row['nis'].'</td>
                <td style="text-align: left;">'.$row['nama_siswa'].'</td>
                <td>'.($row['rata_tugas'] ? number_format($row['rata_tugas'], 2) : '-').'</td>
                <td>'.($row['nilai_uts'] ?? '-').'</td>
                <td>'.($row['nilai_uas'] ?? '-').'</td>
                <td>'.($row['nilai_absen'] ?? '-').'</td>
                <td>'.($row['rumus_nilai'] ?? '-').'</td>
                <td class="'.$color_class.'"><strong>'.($nilai_akhir ?: '-').'</strong></td>
                <td class="'.$color_class.'">'.$status.'</td>
            </tr>';
    }

    $html .= '
            </tbody>
        </table>

        <div style="margin-top: 20px;">
            <p><strong>Keterangan:</strong></p>
            <ul>
                <li><span class="success">Hijau</span> = Nilai memenuhi atau melebihi KKM ('.$mapel->kkm.')</li>
                <li><span class="danger">Merah</span> = Nilai di bawah KKM ('.$mapel->kkm.')</li>
            </ul>
        </div>
    </body>
    </html>';

    $dompdf->loadHtml($html);
    $dompdf->render();

    // Output the generated PDF
    $dompdf->stream(
        'Nilai_' . $mapel->nama_mapel . '_Semester_' . $semester . '_' . $tahun_ajaran . '.pdf',
        array('Attachment' => true)
    );

    exit(0);

} catch (Exception $e) {
    error_log('PDF Export Error: ' . $e->getMessage());
    die("Maaf, terjadi kesalahan saat mengekspor file PDF. Silakan coba lagi. Error: " . $e->getMessage());
}
