<?php
require_once __DIR__ . '/../middleware/auth.php';
checkGuruAccess();
require_once '../vendor/autoload.php';

use Dompdf\Dompdf;
use Dompdf\Options;
use PhpOffice\PhpWord\PhpWord;
use PhpOffice\PhpWord\IOFactory;

// Check if blueprint data exists in session
if (!isset($_SESSION['blueprint_data']) || !isset($_SESSION['blueprint_config'])) {
    $_SESSION['error'] = "Data kisi-kisi tidak ditemukan. Silakan generate ulang.";
    header("Location: generate_questions.php");
    exit();
}

$format = $_GET['format'] ?? 'pdf';
$rpp_id = $_GET['rpp_id'] ?? '';

if (!in_array($format, ['pdf', 'word'])) {
    $_SESSION['error'] = "Format export tidak valid.";
    header("Location: blueprint_result.php?rpp_id=" . $rpp_id);
    exit();
}

$blueprint_data = $_SESSION['blueprint_data'];
$config = $_SESSION['blueprint_config'];

// Get application info
$app_name = "SIHADIR - Sistem Informasi Kehadiran Siswa";
$app_version = "v2.18.0";

try {
    if ($format === 'pdf') {
        exportToPDF($blueprint_data, $config, $app_name, $app_version);
    } else {
        exportToWord($blueprint_data, $config, $app_name, $app_version);
    }
} catch (Exception $e) {
    $_SESSION['error'] = "Gagal export kisi-kisi: " . $e->getMessage();
    header("Location: blueprint_result.php?rpp_id=" . $rpp_id);
    exit();
}

function exportToPDF($blueprint_data, $config, $app_name, $app_version) {
    $options = new Options();
    $options->set('defaultFont', 'Arial');
    $options->set('isRemoteEnabled', true);

    $dompdf = new Dompdf($options);
    
    $html = generateBlueprintHTML($blueprint_data, $config, $app_name, $app_version);
    
    $dompdf->loadHtml($html);
    $dompdf->setPaper('A4', 'portrait');
    $dompdf->render();
    
    $filename = 'Kisi-kisi_' . str_replace(' ', '_', $blueprint_data['exam_info']['subject']) . '_' . date('Y-m-d') . '.pdf';
    $dompdf->stream($filename, array('Attachment' => true));
}

function exportToWord($blueprint_data, $config, $app_name, $app_version) {
    $phpWord = new PhpWord();
    
    // Set document properties
    $properties = $phpWord->getDocInfo();
    $properties->setCreator($app_name . ' ' . $app_version);
    $properties->setTitle('Kisi-kisi Ujian - ' . $blueprint_data['exam_info']['subject']);
    $properties->setDescription('Kisi-kisi ujian yang digenerate otomatis');
    
    $section = $phpWord->addSection();
    
    // Header
    $section->addText('KISI-KISI SOAL PENILAIAN TENGAH SEMESTER', 
        array('bold' => true, 'size' => 14), 
        array('alignment' => 'center'));
    $section->addTextBreak();
    
    // Exam Information
    $tableStyle = array('borderSize' => 6, 'borderColor' => '000000', 'cellMargin' => 80);
    $table = $section->addTable($tableStyle);
    
    $table->addRow();
    $table->addCell(3000)->addText('Mata Pelajaran', array('bold' => true));
    $table->addCell(6000)->addText(': ' . $blueprint_data['exam_info']['subject']);
    
    $table->addRow();
    $table->addCell(3000)->addText('Kelas/Semester', array('bold' => true));
    $table->addCell(6000)->addText(': ' . $blueprint_data['exam_info']['class'] . '/' . $blueprint_data['exam_info']['semester']);
    
    $table->addRow();
    $table->addCell(3000)->addText('Jenis Ujian', array('bold' => true));
    $table->addCell(6000)->addText(': ' . $blueprint_data['exam_info']['exam_type']);
    
    $table->addRow();
    $table->addCell(3000)->addText('Waktu', array('bold' => true));
    $table->addCell(6000)->addText(': ' . $blueprint_data['exam_info']['duration']);
    
    $section->addTextBreak(2);
    
    // Question Mapping Table
    $section->addText('PEMETAAN SOAL', array('bold' => true, 'size' => 12));
    $section->addTextBreak();
    
    $mappingTable = $section->addTable($tableStyle);
    
    // Header row
    $mappingTable->addRow();
    $mappingTable->addCell(800)->addText('No', array('bold' => true), array('alignment' => 'center'));
    $mappingTable->addCell(2500)->addText('Kompetensi Dasar', array('bold' => true), array('alignment' => 'center'));
    $mappingTable->addCell(1500)->addText('Materi', array('bold' => true), array('alignment' => 'center'));
    $mappingTable->addCell(2500)->addText('Indikator Soal', array('bold' => true), array('alignment' => 'center'));
    $mappingTable->addCell(1000)->addText('Level Kognitif', array('bold' => true), array('alignment' => 'center'));
    $mappingTable->addCell(1000)->addText('Bentuk Soal', array('bold' => true), array('alignment' => 'center'));
    $mappingTable->addCell(800)->addText('No Soal', array('bold' => true), array('alignment' => 'center'));
    $mappingTable->addCell(800)->addText('Bobot', array('bold' => true), array('alignment' => 'center'));
    
    // Data rows
    if (isset($blueprint_data['question_mapping'])) {
        foreach ($blueprint_data['question_mapping'] as $mapping) {
            $mappingTable->addRow();
            $mappingTable->addCell(800)->addText($mapping['no'], null, array('alignment' => 'center'));
            $mappingTable->addCell(2500)->addText($mapping['competency']);
            $mappingTable->addCell(1500)->addText($mapping['material']);
            $mappingTable->addCell(2500)->addText($mapping['indicator']);
            $mappingTable->addCell(1000)->addText($mapping['cognitive_level'], null, array('alignment' => 'center'));
            $mappingTable->addCell(1000)->addText($mapping['question_type'], null, array('alignment' => 'center'));
            $mappingTable->addCell(800)->addText($mapping['question_number'], null, array('alignment' => 'center'));
            $mappingTable->addCell(800)->addText($mapping['score_weight'], null, array('alignment' => 'center'));
        }
    }
    
    $section->addTextBreak(2);
    
    // Summary
    $section->addText('RINGKASAN', array('bold' => true, 'size' => 12));
    $section->addTextBreak();
    
    $summaryTable = $section->addTable($tableStyle);
    $summaryTable->addRow();
    $summaryTable->addCell(3000)->addText('Total Soal', array('bold' => true));
    $summaryTable->addCell(6000)->addText(': ' . $blueprint_data['summary']['total_questions']);
    
    $summaryTable->addRow();
    $summaryTable->addCell(3000)->addText('Pilihan Ganda', array('bold' => true));
    $summaryTable->addCell(6000)->addText(': ' . $blueprint_data['summary']['multiple_choice']);
    
    $summaryTable->addRow();
    $summaryTable->addCell(3000)->addText('Essay', array('bold' => true));
    $summaryTable->addCell(6000)->addText(': ' . $blueprint_data['summary']['essay']);
    
    // Footer
    $section->addTextBreak(3);
    $section->addText('Generated by ' . $app_name . ' ' . $app_version . ' on ' . date('d/m/Y H:i:s'), 
        array('size' => 8, 'italic' => true), 
        array('alignment' => 'center'));
    
    $filename = 'Kisi-kisi_' . str_replace(' ', '_', $blueprint_data['exam_info']['subject']) . '_' . date('Y-m-d') . '.docx';
    
    header('Content-Type: application/vnd.openxmlformats-officedocument.wordprocessingml.document');
    header('Content-Disposition: attachment;filename="' . $filename . '"');
    header('Cache-Control: max-age=0');
    
    $objWriter = IOFactory::createWriter($phpWord, 'Word2007');
    $objWriter->save('php://output');
}

function generateBlueprintHTML($blueprint_data, $config, $app_name, $app_version) {
    ob_start();
    ?>
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>Kisi-kisi Ujian</title>
        <style>
            body { font-family: Arial, sans-serif; font-size: 12px; margin: 20px; }
            .header { text-align: center; margin-bottom: 30px; }
            .title { font-size: 16px; font-weight: bold; margin-bottom: 20px; }
            .info-table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
            .info-table td { padding: 5px; border: 1px solid #000; }
            .mapping-table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
            .mapping-table th, .mapping-table td { padding: 8px; border: 1px solid #000; text-align: center; }
            .mapping-table th { background-color: #f0f0f0; font-weight: bold; }
            .summary { margin-top: 20px; }
            .footer { text-align: center; margin-top: 30px; font-size: 10px; color: #666; }
        </style>
    </head>
    <body>
        <div class="header">
            <div class="title">KISI-KISI SOAL PENILAIAN TENGAH SEMESTER</div>
        </div>
        
        <table class="info-table">
            <tr>
                <td width="150"><strong>Mata Pelajaran</strong></td>
                <td>: <?= htmlspecialchars($blueprint_data['exam_info']['subject']) ?></td>
            </tr>
            <tr>
                <td><strong>Kelas/Semester</strong></td>
                <td>: <?= htmlspecialchars($blueprint_data['exam_info']['class']) ?>/<?= htmlspecialchars($blueprint_data['exam_info']['semester']) ?></td>
            </tr>
            <tr>
                <td><strong>Jenis Ujian</strong></td>
                <td>: <?= htmlspecialchars($blueprint_data['exam_info']['exam_type']) ?></td>
            </tr>
            <tr>
                <td><strong>Waktu</strong></td>
                <td>: <?= htmlspecialchars($blueprint_data['exam_info']['duration']) ?></td>
            </tr>
        </table>
        
        <h3>PEMETAAN SOAL</h3>
        <table class="mapping-table">
            <thead>
                <tr>
                    <th width="5%">No</th>
                    <th width="20%">Kompetensi Dasar</th>
                    <th width="15%">Materi</th>
                    <th width="25%">Indikator Soal</th>
                    <th width="10%">Level Kognitif</th>
                    <th width="10%">Bentuk Soal</th>
                    <th width="8%">No Soal</th>
                    <th width="7%">Bobot</th>
                </tr>
            </thead>
            <tbody>
                <?php if (isset($blueprint_data['question_mapping'])): ?>
                    <?php foreach ($blueprint_data['question_mapping'] as $mapping): ?>
                        <tr>
                            <td><?= htmlspecialchars($mapping['no']) ?></td>
                            <td style="text-align: left;"><?= htmlspecialchars($mapping['competency']) ?></td>
                            <td style="text-align: left;"><?= htmlspecialchars($mapping['material']) ?></td>
                            <td style="text-align: left;"><?= htmlspecialchars($mapping['indicator']) ?></td>
                            <td><?= htmlspecialchars($mapping['cognitive_level']) ?></td>
                            <td><?= htmlspecialchars($mapping['question_type']) ?></td>
                            <td><?= htmlspecialchars($mapping['question_number']) ?></td>
                            <td><?= htmlspecialchars($mapping['score_weight']) ?></td>
                        </tr>
                    <?php endforeach; ?>
                <?php endif; ?>
            </tbody>
        </table>
        
        <div class="summary">
            <h3>RINGKASAN</h3>
            <table class="info-table">
                <tr>
                    <td width="150"><strong>Total Soal</strong></td>
                    <td>: <?= htmlspecialchars($blueprint_data['summary']['total_questions']) ?></td>
                </tr>
                <tr>
                    <td><strong>Pilihan Ganda</strong></td>
                    <td>: <?= htmlspecialchars($blueprint_data['summary']['multiple_choice']) ?></td>
                </tr>
                <tr>
                    <td><strong>Essay</strong></td>
                    <td>: <?= htmlspecialchars($blueprint_data['summary']['essay']) ?></td>
                </tr>
            </table>
        </div>
        
        <div class="footer">
            Generated by <?= htmlspecialchars($app_name) ?> <?= htmlspecialchars($app_version) ?> on <?= date('d/m/Y H:i:s') ?>
        </div>
    </body>
    </html>
    <?php
    return ob_get_clean();
}
?>
