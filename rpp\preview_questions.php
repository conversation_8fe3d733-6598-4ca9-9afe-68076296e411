<?php
require_once __DIR__ . '/../middleware/auth.php';
checkGuruAccess();
require_once '../template/header.php';
require_once '../config/database.php';
require_once '../models/Rpp.php';
require_once '../models/Guru.php';
require_once '../models/GeminiApi.php';

// Cek jika bukan POST request
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    $_SESSION['error'] = "Akses tidak valid.";
    header("Location: generate_questions.php");
    exit();
}

// Validasi input
$required_fields = ['rpp_id', 'multiple_choice_count', 'essay_count', 'multiple_choice_options', 
                   'regular_count', 'hots_easy_count', 'hots_medium_count', 'hots_hard_count'];

foreach ($required_fields as $field) {
    if (!isset($_POST[$field])) {
        $_SESSION['error'] = "Data konfigurasi tidak lengkap.";
        header("Location: generate_questions.php");
        exit();
    }
}

$rpp_id = $_POST['rpp_id'];
$config = [
    'multiple_choice_count' => (int)$_POST['multiple_choice_count'],
    'essay_count' => (int)$_POST['essay_count'],
    'multiple_choice_options' => (int)$_POST['multiple_choice_options'],
    'regular_count' => (int)$_POST['regular_count'],
    'hots_easy_count' => (int)$_POST['hots_easy_count'],
    'hots_medium_count' => (int)$_POST['hots_medium_count'],
    'hots_hard_count' => (int)$_POST['hots_hard_count']
];

// Validasi total soal
$total_by_type = $config['multiple_choice_count'] + $config['essay_count'];
$total_by_difficulty = $config['regular_count'] + $config['hots_easy_count'] + 
                      $config['hots_medium_count'] + $config['hots_hard_count'];
$max_total = max($total_by_type, $total_by_difficulty);

if ($max_total > 10 || $max_total === 0) {
    $_SESSION['error'] = "Total soal harus antara 1-10.";
    header("Location: configure_generation.php?rpp_id=" . $rpp_id);
    exit();
}

// Dapatkan guru_id
$guru = new Guru();
$stmt = $guru->getByUserId($_SESSION['user_id']);
if ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
    $guru_id = $row['id'];
} else {
    die("Data guru tidak ditemukan");
}

// Ambil data RPP
$rpp = new Rpp();
$stmt = $rpp->getOne($rpp_id);
$rpp_data = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$rpp_data || $rpp_data['guru_id'] != $guru_id) {
    $_SESSION['error'] = "RPP tidak ditemukan atau bukan milik Anda.";
    header("Location: generate_questions.php");
    exit();
}

// Generate questions using Gemini API
$generated_questions = [];
$error_message = '';
$loading = true;

try {
    $geminiApi = new GeminiApi();
    $generated_questions = $geminiApi->generateQuestions($rpp_data, $config);
    $loading = false;
} catch (Exception $e) {
    $error_message = $e->getMessage();
    $loading = false;
}
?>

<div class="container-fluid">
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">Preview Soal yang Dihasilkan</h5>
            <a href="configure_generation.php?rpp_id=<?= $rpp_id ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Kembali ke Konfigurasi
            </a>
        </div>
        <div class="card-body">
            <!-- RPP Info -->
            <div class="alert alert-info">
                <strong>RPP:</strong> <?= htmlspecialchars($rpp_data['nama_mapel']) ?> - 
                <?= htmlspecialchars($rpp_data['nama_kelas']) ?> - 
                <?= htmlspecialchars($rpp_data['materi_pokok']) ?>
            </div>

            <?php if ($loading): ?>
                <!-- Loading State -->
                <div class="text-center py-5">
                    <div class="spinner-border text-primary" role="status" style="width: 3rem; height: 3rem;">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <h5 class="mt-3">Sedang Generate Soal...</h5>
                    <p class="text-muted">Mohon tunggu, AI sedang membuat soal berdasarkan RPP Anda.</p>
                </div>
            <?php elseif ($error_message): ?>
                <!-- Error State -->
                <div class="alert alert-danger">
                    <h6><i class="fas fa-exclamation-triangle"></i> Gagal Generate Soal</h6>
                    <p class="mb-0"><?= htmlspecialchars($error_message) ?></p>
                </div>
                <div class="text-center">
                    <a href="configure_generation.php?rpp_id=<?= $rpp_id ?>" class="btn btn-primary">
                        <i class="fas fa-redo"></i> Coba Lagi
                    </a>
                </div>
            <?php elseif (empty($generated_questions)): ?>
                <!-- No Questions Generated -->
                <div class="alert alert-warning">
                    <h6><i class="fas fa-exclamation-circle"></i> Tidak Ada Soal yang Dihasilkan</h6>
                    <p class="mb-0">Sistem tidak berhasil menghasilkan soal. Silakan coba lagi dengan konfigurasi yang berbeda.</p>
                </div>
                <div class="text-center">
                    <a href="configure_generation.php?rpp_id=<?= $rpp_id ?>" class="btn btn-primary">
                        <i class="fas fa-redo"></i> Coba Lagi
                    </a>
                </div>
            <?php else: ?>
                <!-- Success State - Show Generated Questions -->
                <form action="save_questions.php" method="POST">
                    <input type="hidden" name="rpp_id" value="<?= $rpp_id ?>">
                    
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h6>Pilih soal yang akan disimpan:</h6>
                        <div>
                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="selectAll()">
                                <i class="fas fa-check-square"></i> Pilih Semua
                            </button>
                            <button type="button" class="btn btn-outline-secondary btn-sm" onclick="deselectAll()">
                                <i class="fas fa-square"></i> Batal Pilih
                            </button>
                        </div>
                    </div>

                    <div class="row">
                        <?php foreach ($generated_questions as $index => $question): ?>
                            <div class="col-12 mb-4">
                                <div class="card">
                                    <div class="card-header">
                                        <div class="form-check">
                                            <input class="form-check-input question-checkbox" type="checkbox" 
                                                   name="selected_questions[]" value="<?= $index ?>" 
                                                   id="question_<?= $index ?>" checked>
                                            <label class="form-check-label fw-bold" for="question_<?= $index ?>">
                                                Soal <?= $index + 1 ?>
                                                <span class="badge bg-<?= $question['question_type'] === 'multiple_choice' ? 'primary' : 'success' ?> ms-2">
                                                    <?= $question['question_type'] === 'multiple_choice' ? 'Pilihan Ganda' : 'Essay' ?>
                                                </span>
                                                <span class="badge bg-<?= getDifficultyColor($question['difficulty_level']) ?> ms-1">
                                                    <?= getDifficultyLabel($question['difficulty_level']) ?>
                                                </span>
                                            </label>
                                        </div>
                                    </div>
                                    <div class="card-body">
                                        <div class="question-text mb-3">
                                            <?= nl2br(htmlspecialchars($question['question_text'])) ?>
                                        </div>

                                        <?php if ($question['question_type'] === 'multiple_choice' && $question['options']): ?>
                                            <?php $options = json_decode($question['options'], true); ?>
                                            <?php if ($options): ?>
                                                <div class="options mb-3">
                                                    <?php foreach ($options as $option): ?>
                                                        <div class="option-item mb-1 
                                                            <?= (substr($option, 0, 1) === $question['correct_answer']) ? 'correct-answer' : '' ?>">
                                                            <?= htmlspecialchars($option) ?>
                                                            <?php if (substr($option, 0, 1) === $question['correct_answer']): ?>
                                                                <i class="fas fa-check-circle text-success ms-2"></i>
                                                            <?php endif; ?>
                                                        </div>
                                                    <?php endforeach; ?>
                                                </div>
                                                <div class="answer-key">
                                                    <small class="text-muted">
                                                        <strong>Kunci Jawaban:</strong> <?= htmlspecialchars($question['correct_answer']) ?>
                                                    </small>
                                                </div>
                                            <?php endif; ?>
                                        <?php endif; ?>

                                        <!-- Hidden inputs for question data -->
                                        <input type="hidden" name="questions[<?= $index ?>][question_text]" 
                                               value="<?= htmlspecialchars($question['question_text']) ?>">
                                        <input type="hidden" name="questions[<?= $index ?>][question_type]" 
                                               value="<?= htmlspecialchars($question['question_type']) ?>">
                                        <input type="hidden" name="questions[<?= $index ?>][difficulty_level]" 
                                               value="<?= htmlspecialchars($question['difficulty_level']) ?>">
                                        <input type="hidden" name="questions[<?= $index ?>][options]" 
                                               value="<?= htmlspecialchars($question['options']) ?>">
                                        <input type="hidden" name="questions[<?= $index ?>][correct_answer]" 
                                               value="<?= htmlspecialchars($question['correct_answer']) ?>">
                                        <input type="hidden" name="questions[<?= $index ?>][category]" 
                                               value="<?= htmlspecialchars($question['category']) ?>">
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>

                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <button type="submit" class="btn btn-success btn-lg">
                            <i class="fas fa-save"></i> Simpan Soal Terpilih
                        </button>
                    </div>
                </form>
            <?php endif; ?>
        </div>
    </div>
</div>

<style>
.question-text {
    font-size: 1.1rem;
    line-height: 1.6;
}

.option-item {
    padding: 8px 12px;
    border-radius: 4px;
    background-color: #f8f9fa;
    border-left: 3px solid #dee2e6;
}

.option-item.correct-answer {
    background-color: #d4edda;
    border-left-color: #28a745;
    font-weight: 500;
}

.form-check-input:checked {
    background-color: #28a745;
    border-color: #28a745;
}

.card-header .form-check {
    margin-bottom: 0;
}

.badge {
    font-size: 0.8rem;
}
</style>

<script>
function selectAll() {
    document.querySelectorAll('.question-checkbox').forEach(checkbox => {
        checkbox.checked = true;
    });
}

function deselectAll() {
    document.querySelectorAll('.question-checkbox').forEach(checkbox => {
        checkbox.checked = false;
    });
}
</script>

<?php
function getDifficultyColor($difficulty) {
    switch ($difficulty) {
        case 'regular': return 'secondary';
        case 'hots_easy': return 'info';
        case 'hots_medium': return 'warning';
        case 'hots_hard': return 'danger';
        default: return 'secondary';
    }
}

function getDifficultyLabel($difficulty) {
    switch ($difficulty) {
        case 'regular': return 'Regular';
        case 'hots_easy': return 'HOTS Mudah';
        case 'hots_medium': return 'HOTS Sedang';
        case 'hots_hard': return 'HOTS Tinggi';
        default: return 'Regular';
    }
}
?>

<?php require_once '../template/footer.php'; ?>
