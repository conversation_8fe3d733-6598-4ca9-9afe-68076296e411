<?php
require_once __DIR__ . '/../middleware/auth.php';
checkGuruAccess();
require_once '../template/header.php';
require_once '../config/database.php';
require_once '../models/Rpp.php';
require_once '../models/Guru.php';

// Cek jika bukan POST request
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    $_SESSION['error'] = "Akses tidak valid.";
    header("Location: generate_questions.php");
    exit();
}

// Validasi input
$required_fields = ['rpp_id', 'multiple_choice_count', 'essay_count', 'multiple_choice_options', 
                   'regular_count', 'hots_easy_count', 'hots_medium_count', 'hots_hard_count'];

foreach ($required_fields as $field) {
    if (!isset($_POST[$field])) {
        $_SESSION['error'] = "Data konfigurasi tidak lengkap.";
        header("Location: generate_questions.php");
        exit();
    }
}

$rpp_id = $_POST['rpp_id'];
$config = [
    'multiple_choice_count' => (int)$_POST['multiple_choice_count'],
    'essay_count' => (int)$_POST['essay_count'],
    'multiple_choice_options' => (int)$_POST['multiple_choice_options'],
    'regular_count' => (int)$_POST['regular_count'],
    'hots_easy_count' => (int)$_POST['hots_easy_count'],
    'hots_medium_count' => (int)$_POST['hots_medium_count'],
    'hots_hard_count' => (int)$_POST['hots_hard_count']
];

// Validasi total soal
$total_by_type = $config['multiple_choice_count'] + $config['essay_count'];
$total_by_difficulty = $config['regular_count'] + $config['hots_easy_count'] + 
                      $config['hots_medium_count'] + $config['hots_hard_count'];
$max_total = max($total_by_type, $total_by_difficulty);

if ($max_total > 10 || $max_total === 0) {
    $_SESSION['error'] = "Total soal harus antara 1-10.";
    header("Location: configure_generation.php?rpp_id=" . $rpp_id);
    exit();
}

// Dapatkan guru_id
$guru = new Guru();
$stmt = $guru->getByUserId($_SESSION['user_id']);
if ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
    $guru_id = $row['id'];
} else {
    die("Data guru tidak ditemukan");
}

// Ambil data RPP
$rpp = new Rpp();
$stmt = $rpp->getOne($rpp_id);
$rpp_data = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$rpp_data || $rpp_data['guru_id'] != $guru_id) {
    $_SESSION['error'] = "RPP tidak ditemukan atau bukan milik Anda.";
    header("Location: generate_questions.php");
    exit();
}

// Store configuration for AJAX processing
$_SESSION['question_config'] = $config;
$_SESSION['current_rpp_id'] = $rpp_id;
?>

<div class="container-fluid">
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">Generate Soal AI</h5>
            <a href="configure_generation.php?rpp_id=<?= $rpp_id ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Kembali ke Konfigurasi
            </a>
        </div>
        <div class="card-body">
            <!-- RPP Info -->
            <div class="alert alert-info">
                <strong>RPP:</strong> <?= htmlspecialchars($rpp_data['nama_mapel']) ?> - 
                <?= htmlspecialchars($rpp_data['nama_kelas']) ?> - 
                <?= htmlspecialchars($rpp_data['materi_pokok']) ?>
            </div>

            <!-- Configuration Summary -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card bg-light">
                        <div class="card-body">
                            <h6 class="card-title">Konfigurasi Soal</h6>
                            <ul class="list-unstyled mb-0">
                                <li><strong>Pilihan Ganda:</strong> <?= $config['multiple_choice_count'] ?> soal</li>
                                <li><strong>Essay:</strong> <?= $config['essay_count'] ?> soal</li>
                                <li><strong>Total:</strong> <?= $max_total ?> soal</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card bg-light">
                        <div class="card-body">
                            <h6 class="card-title">Tingkat Kesulitan</h6>
                            <ul class="list-unstyled mb-0">
                                <li><strong>Regular:</strong> <?= $config['regular_count'] ?> soal</li>
                                <li><strong>HOTS Mudah:</strong> <?= $config['hots_easy_count'] ?> soal</li>
                                <li><strong>HOTS Sedang:</strong> <?= $config['hots_medium_count'] ?> soal</li>
                                <li><strong>HOTS Tinggi:</strong> <?= $config['hots_hard_count'] ?> soal</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Generation Status -->
            <div id="generation-status">
                <!-- Initial State -->
                <div class="text-center py-4" id="initial-state">
                    <div class="mb-3">
                        <i class="fas fa-robot fa-3x text-primary"></i>
                    </div>
                    <h5>Siap Generate Soal dengan AI</h5>
                    <p class="text-muted">Klik tombol di bawah untuk memulai proses generate soal menggunakan AI</p>
                    <button type="button" class="btn btn-primary btn-lg" id="start-generation">
                        <i class="fas fa-play"></i> Mulai Generate Soal
                    </button>
                </div>

                <!-- Loading State -->
                <div class="text-center py-5 d-none" id="loading-state">
                    <div class="spinner-border text-primary mb-3" role="status" style="width: 3rem; height: 3rem;">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <h5 id="loading-title">Sedang Generate Soal...</h5>
                    <p class="text-muted" id="loading-message">Mohon tunggu, AI sedang membuat soal berdasarkan RPP Anda.</p>
                    
                    <!-- Progress Bar -->
                    <div class="progress mx-auto mt-3" style="width: 60%; height: 8px;">
                        <div class="progress-bar progress-bar-striped progress-bar-animated" 
                             role="progressbar" style="width: 0%" id="progress-bar"></div>
                    </div>
                    <small class="text-muted d-block mt-2" id="progress-text">Memulai...</small>
                    
                    <!-- Timeout Warning -->
                    <div class="alert alert-warning mt-3 d-none" id="timeout-warning">
                        <i class="fas fa-clock"></i> Proses memakan waktu lebih lama dari biasanya. Mohon bersabar...
                    </div>
                    
                    <!-- Cancel Button -->
                    <button type="button" class="btn btn-outline-secondary mt-3" id="cancel-generation">
                        <i class="fas fa-times"></i> Batalkan
                    </button>
                </div>

                <!-- Error State -->
                <div class="alert alert-danger d-none" id="error-state">
                    <h6><i class="fas fa-exclamation-triangle"></i> Gagal Generate Soal</h6>
                    <p class="mb-3" id="error-message"></p>
                    <div class="d-flex gap-2 justify-content-center">
                        <button type="button" class="btn btn-primary" id="retry-generation">
                            <i class="fas fa-redo"></i> Coba Lagi
                        </button>
                        <a href="configure_generation.php?rpp_id=<?= $rpp_id ?>" class="btn btn-secondary">
                            <i class="fas fa-cog"></i> Ubah Konfigurasi
                        </a>
                    </div>
                </div>

                <!-- Success State -->
                <div class="d-none" id="success-state">
                    <div class="alert alert-success">
                        <h6><i class="fas fa-check-circle"></i> Soal Berhasil Dihasilkan!</h6>
                        <p class="mb-0">AI telah berhasil membuat <span id="total-questions"></span> soal berdasarkan RPP Anda.</p>
                    </div>
                    
                    <!-- Questions will be loaded here -->
                    <div id="questions-container"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.question-text {
    font-size: 1.1rem;
    line-height: 1.6;
}

.option-item {
    padding: 8px 12px;
    border-radius: 4px;
    background-color: #f8f9fa;
    border-left: 3px solid #dee2e6;
}

.option-item.correct-answer {
    background-color: #d4edda;
    border-left-color: #28a745;
    font-weight: 500;
}

.form-check-input:checked {
    background-color: #28a745;
    border-color: #28a745;
}

.card-header .form-check {
    margin-bottom: 0;
}

.badge {
    font-size: 0.8rem;
}

#generation-status {
    min-height: 300px;
}

.progress {
    border-radius: 10px;
}

.progress-bar {
    transition: width 0.3s ease;
}
</style>

<script>
let generationInProgress = false;
let generationTimeout = null;
let progressInterval = null;
let retryCount = 0;
const maxRetries = 2;

document.addEventListener('DOMContentLoaded', function() {
    const startBtn = document.getElementById('start-generation');
    const retryBtn = document.getElementById('retry-generation');
    const cancelBtn = document.getElementById('cancel-generation');
    
    startBtn.addEventListener('click', startGeneration);
    retryBtn.addEventListener('click', startGeneration);
    cancelBtn.addEventListener('click', cancelGeneration);
});

function startGeneration(isRetry = false) {
    if (generationInProgress) return;

    if (!isRetry) {
        retryCount = 0;
    }

    generationInProgress = true;
    showLoadingState();
    
    // Set timeout warning after 30 seconds
    generationTimeout = setTimeout(() => {
        document.getElementById('timeout-warning').classList.remove('d-none');
    }, 30000);
    
    // Start progress simulation
    simulateProgress();
    
    // Prepare form data
    const formData = new FormData();
    formData.append('rpp_id', '<?= $rpp_id ?>');
    formData.append('multiple_choice_count', '<?= $config['multiple_choice_count'] ?>');
    formData.append('essay_count', '<?= $config['essay_count'] ?>');
    formData.append('multiple_choice_options', '<?= $config['multiple_choice_options'] ?>');
    formData.append('regular_count', '<?= $config['regular_count'] ?>');
    formData.append('hots_easy_count', '<?= $config['hots_easy_count'] ?>');
    formData.append('hots_medium_count', '<?= $config['hots_medium_count'] ?>');
    formData.append('hots_hard_count', '<?= $config['hots_hard_count'] ?>');
    
    // Make AJAX request with enhanced error handling
    fetch('ajax_generate_questions.php', {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => {
        // Check if response is ok
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        // Check content type
        const contentType = response.headers.get('content-type');
        if (!contentType || !contentType.includes('application/json')) {
            throw new Error('Response bukan format JSON yang valid');
        }

        return response.json();
    })
    .then(data => {
        clearTimeout(generationTimeout);
        clearInterval(progressInterval);
        generationInProgress = false;

        if (data.success) {
            showSuccessState(data.data);
        } else {
            // Enhanced error handling with error types
            showErrorStateWithType(data.message, data.error_type, data.server_info);
        }
    })
    .catch(error => {
        clearTimeout(generationTimeout);
        clearInterval(progressInterval);
        generationInProgress = false;

        console.error('Generation error:', error);

        // Determine error type and provide specific message
        let errorMessage = 'Terjadi kesalahan jaringan. Silakan coba lagi.';
        let errorType = 'network';
        let shouldRetry = false;

        if (error.message.includes('HTTP 404')) {
            errorMessage = 'Endpoint tidak ditemukan. Silakan hubungi administrator.';
            errorType = 'endpoint';
        } else if (error.message.includes('HTTP 500')) {
            errorMessage = 'Kesalahan server internal. Silakan coba lagi dalam beberapa saat.';
            errorType = 'server';
            shouldRetry = true;
        } else if (error.message.includes('JSON')) {
            errorMessage = 'Response server tidak valid. Silakan coba lagi.';
            errorType = 'response';
            shouldRetry = true;
        } else if (error.message.includes('Failed to fetch')) {
            errorMessage = 'Tidak dapat terhubung ke server. Periksa koneksi internet Anda.';
            errorType = 'connection';
            shouldRetry = true;
        }

        // Automatic retry for transient errors
        if (shouldRetry && retryCount < maxRetries) {
            retryCount++;
            console.log(`Retrying request (attempt ${retryCount}/${maxRetries})`);

            // Show retry message
            updateProgress(10, `Mencoba ulang... (percobaan ${retryCount}/${maxRetries})`);

            // Retry after a short delay
            setTimeout(() => {
                startGeneration(true);
            }, 2000 * retryCount); // Exponential backoff

            return;
        }

        showErrorStateWithType(errorMessage, errorType);
    });
}

function cancelGeneration() {
    if (!generationInProgress) return;
    
    clearTimeout(generationTimeout);
    clearInterval(progressInterval);
    generationInProgress = false;
    
    showInitialState();
}

function showInitialState() {
    document.getElementById('initial-state').classList.remove('d-none');
    document.getElementById('loading-state').classList.add('d-none');
    document.getElementById('error-state').classList.add('d-none');
    document.getElementById('success-state').classList.add('d-none');
}

function showLoadingState() {
    document.getElementById('initial-state').classList.add('d-none');
    document.getElementById('loading-state').classList.remove('d-none');
    document.getElementById('error-state').classList.add('d-none');
    document.getElementById('success-state').classList.add('d-none');
    document.getElementById('timeout-warning').classList.add('d-none');
    
    // Reset progress
    updateProgress(0, 'Memulai proses generate...');
}

function showErrorState(message) {
    showErrorStateWithType(message, 'general');
}

function showErrorStateWithType(message, errorType, serverInfo) {
    document.getElementById('initial-state').classList.add('d-none');
    document.getElementById('loading-state').classList.add('d-none');
    document.getElementById('error-state').classList.remove('d-none');
    document.getElementById('success-state').classList.add('d-none');

    const errorMessageEl = document.getElementById('error-message');
    errorMessageEl.innerHTML = message;

    // Add error type specific styling and suggestions
    const errorStateEl = document.getElementById('error-state');

    // Remove existing error type classes
    errorStateEl.classList.remove('error-network', 'error-timeout', 'error-api', 'error-config');

    // Add specific error type class
    if (errorType) {
        errorStateEl.classList.add('error-' + errorType);
    }

    // Add specific suggestions based on error type
    let suggestions = '';
    switch (errorType) {
        case 'network':
        case 'connection':
            suggestions = '<div class="mt-2"><small class="text-muted"><strong>Saran:</strong> Periksa koneksi internet, refresh halaman, atau coba lagi dalam beberapa saat.</small></div>';
            break;
        case 'timeout':
            suggestions = '<div class="mt-2"><small class="text-muted"><strong>Saran:</strong> Kurangi jumlah soal atau coba lagi dengan koneksi yang lebih stabil.</small></div>';
            break;
        case 'api':
            suggestions = '<div class="mt-2"><small class="text-muted"><strong>Saran:</strong> Layanan AI sedang sibuk. Coba lagi dalam beberapa menit.</small></div>';
            break;
        case 'config':
        case 'auth':
            suggestions = '<div class="mt-2"><small class="text-muted"><strong>Saran:</strong> Hubungi administrator untuk memperbaiki konfigurasi sistem.</small></div>';
            break;
        case 'endpoint':
            suggestions = '<div class="mt-2"><small class="text-muted"><strong>Saran:</strong> <a href="network_diagnostic.php" target="_blank">Jalankan diagnostic tool</a> untuk informasi lebih detail.</small></div>';
            break;
    }

    if (suggestions) {
        errorMessageEl.innerHTML += suggestions;
    }

    // Add server info if available
    if (serverInfo) {
        const debugInfo = `<div class="mt-3 p-2" style="background: #f8f9fa; border-radius: 3px; font-size: 0.8em;">
            <strong>Debug Info:</strong><br>
            PHP: ${serverInfo.php_version} |
            Memory: ${serverInfo.memory_usage} |
            Time: ${serverInfo.execution_time}
        </div>`;
        errorMessageEl.innerHTML += debugInfo;
    }
}

function showSuccessState(data) {
    document.getElementById('initial-state').classList.add('d-none');
    document.getElementById('loading-state').classList.add('d-none');
    document.getElementById('error-state').classList.add('d-none');
    document.getElementById('success-state').classList.remove('d-none');
    
    document.getElementById('total-questions').textContent = data.total_questions;
    
    // Load questions
    loadQuestions(data.questions);
}

function simulateProgress() {
    let progress = 0;
    const messages = [
        'Memulai proses generate...',
        'Menghubungi AI Gemini...',
        'Menganalisis RPP...',
        'Membuat soal pilihan ganda...',
        'Membuat soal essay...',
        'Memvalidasi hasil...',
        'Menyelesaikan proses...'
    ];
    
    progressInterval = setInterval(() => {
        progress += Math.random() * 15;
        if (progress > 90) progress = 90; // Don't reach 100% until actually done
        
        const messageIndex = Math.floor((progress / 100) * messages.length);
        const message = messages[Math.min(messageIndex, messages.length - 1)];
        
        updateProgress(progress, message);
    }, 2000);
}

function updateProgress(percent, message) {
    document.getElementById('progress-bar').style.width = percent + '%';
    document.getElementById('progress-text').textContent = message;
}

function loadQuestions(questions) {
    // Validate questions data
    if (!Array.isArray(questions)) {
        console.error('Questions data is not an array:', questions);
        showErrorState('Invalid questions data format');
        return;
    }

    if (questions.length === 0) {
        showErrorState('No questions were generated');
        return;
    }

    const container = document.getElementById('questions-container');
    
    let html = `
        <form action="save_questions.php" method="POST">
            <input type="hidden" name="rpp_id" value="<?= $rpp_id ?>">
            
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h6>Pilih soal yang akan disimpan:</h6>
                <div>
                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="selectAll()">
                        <i class="fas fa-check-square"></i> Pilih Semua
                    </button>
                    <button type="button" class="btn btn-outline-secondary btn-sm" onclick="deselectAll()">
                        <i class="fas fa-square"></i> Batal Pilih
                    </button>
                </div>
            </div>
            
            <div class="row">
    `;
    
    questions.forEach((question, index) => {
        try {
            // Validate question data
            if (!question || typeof question !== 'object') {
                console.warn(`Question ${index + 1} is invalid:`, question);
                return;
            }

            if (!question.question_text || !question.question_type) {
                console.warn(`Question ${index + 1} missing required fields:`, question);
                return;
            }

            html += generateQuestionHTML(question, index);
        } catch (error) {
            console.error(`Error generating HTML for question ${index + 1}:`, error, question);
            html += `
                <div class="col-12 mb-4">
                    <div class="alert alert-danger">
                        <strong>Error loading question ${index + 1}:</strong> ${error.message}
                    </div>
                </div>
            `;
        }
    });
    
    html += `
            </div>
            
            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                <button type="submit" class="btn btn-success btn-lg">
                    <i class="fas fa-save"></i> Simpan Soal Terpilih
                </button>
            </div>
        </form>
    `;
    
    container.innerHTML = html;
}

function getOptionsValue(options) {
    // Helper function to properly format options for hidden input
    if (!options) {
        return '';
    }

    if (Array.isArray(options)) {
        return JSON.stringify(options);
    } else if (typeof options === 'string') {
        // If it's already a JSON string, return as-is
        // If it's a comma-separated string, convert to JSON array
        if (options.trim() === '') {
            return '';
        }

        try {
            // Test if it's valid JSON
            JSON.parse(options);
            return options; // Already valid JSON
        } catch (e) {
            // Not valid JSON, treat as comma-separated string
            if (options.includes(',')) {
                const optionsArray = options.split(',').map(opt => opt.trim()).filter(opt => opt.length > 0);
                return JSON.stringify(optionsArray);
            } else {
                // Single option, wrap in array
                return JSON.stringify([options]);
            }
        }
    }

    return '';
}

function generateQuestionHTML(question, index) {
    const difficultyColors = {
        'regular': 'secondary',
        'hots_easy': 'info',
        'hots_medium': 'warning',
        'hots_hard': 'danger'
    };
    
    const difficultyLabels = {
        'regular': 'Regular',
        'hots_easy': 'HOTS Mudah',
        'hots_medium': 'HOTS Sedang',
        'hots_hard': 'HOTS Tinggi'
    };
    
    let optionsHTML = '';
    if (question.question_type === 'multiple_choice' && question.options) {
        let options = [];

        try {
            // Handle both array and JSON string formats
            if (Array.isArray(question.options)) {
                // Already an array
                options = question.options;
            } else if (typeof question.options === 'string') {
                // Try to parse as JSON string
                if (question.options.trim() === '') {
                    options = [];
                } else {
                    // Check if it's a comma-separated string (fallback format)
                    if (question.options.includes(',') && !question.options.trim().startsWith('[')) {
                        // Split comma-separated string
                        options = question.options.split(',').map(opt => opt.trim()).filter(opt => opt.length > 0);
                    } else {
                        // Try to parse as JSON
                        options = JSON.parse(question.options);
                    }
                }
            }

            if (options.length > 0) {
                optionsHTML = '<div class="options mb-3">';
                options.forEach(option => {
                    const isCorrect = option.charAt(0) === question.correct_answer;
                    optionsHTML += `
                        <div class="option-item mb-1 ${isCorrect ? 'correct-answer' : ''}">
                            ${option}
                            ${isCorrect ? '<i class="fas fa-check-circle text-success ms-2"></i>' : ''}
                        </div>
                    `;
                });
                optionsHTML += '</div>';
                optionsHTML += `<div class="answer-key"><small class="text-muted"><strong>Kunci Jawaban:</strong> ${question.correct_answer}</small></div>`;
            }
        } catch (error) {
            console.error('Error parsing question options:', error, 'Options data:', question.options);
            optionsHTML = '<div class="alert alert-warning"><small>Error parsing options data</small></div>';
        }
    }
    
    return `
        <div class="col-12 mb-4">
            <div class="card">
                <div class="card-header">
                    <div class="form-check">
                        <input class="form-check-input question-checkbox" type="checkbox" 
                               name="selected_questions[]" value="${index}" 
                               id="question_${index}" checked>
                        <label class="form-check-label fw-bold" for="question_${index}">
                            Soal ${index + 1}
                            <span class="badge bg-${question.question_type === 'multiple_choice' ? 'primary' : 'success'} ms-2">
                                ${question.question_type === 'multiple_choice' ? 'Pilihan Ganda' : 'Essay'}
                            </span>
                            <span class="badge bg-${difficultyColors[question.difficulty_level]} ms-1">
                                ${difficultyLabels[question.difficulty_level]}
                            </span>
                        </label>
                    </div>
                </div>
                <div class="card-body">
                    <div class="question-text mb-3">
                        ${question.question_text.replace(/\n/g, '<br>')}
                    </div>
                    ${optionsHTML}
                    
                    <!-- Hidden inputs for question data -->
                    <input type="hidden" name="questions[${index}][question_text]" value="${question.question_text}">
                    <input type="hidden" name="questions[${index}][question_type]" value="${question.question_type}">
                    <input type="hidden" name="questions[${index}][difficulty_level]" value="${question.difficulty_level}">
                    <input type="hidden" name="questions[${index}][options]" value="${getOptionsValue(question.options)}">
                    <input type="hidden" name="questions[${index}][correct_answer]" value="${question.correct_answer || ''}">
                    <input type="hidden" name="questions[${index}][category]" value="${question.category || 'Generated'}">
                </div>
            </div>
        </div>
    `;
}

function selectAll() {
    document.querySelectorAll('.question-checkbox').forEach(checkbox => {
        checkbox.checked = true;
    });
}

function deselectAll() {
    document.querySelectorAll('.question-checkbox').forEach(checkbox => {
        checkbox.checked = false;
    });
}
</script>

<?php require_once '../template/footer.php'; ?>
