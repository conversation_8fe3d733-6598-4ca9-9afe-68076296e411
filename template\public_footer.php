    </div>
    <!-- Footer -->
    <footer class="footer text-center">
        <div class="container">
            <span class="text-muted">
                SIHADIR - Sistem Informasi Kehadiran Siswa &copy; <?php echo date('Y'); ?> | Versi 2.18.0
            </span>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.7/js/dataTables.bootstrap5.min.js"></script>
    <!-- Custom JS -->
    <script>
        // Common JavaScript functions

        // Show loading spinner
        function showLoading() {
            $('#loadingSpinner').show();
        }

        // Hide loading spinner
        function hideLoading() {
            $('#loadingSpinner').hide();
        }

        // Show alert message
        function showAlert(message, type = 'success') {
            const alertDiv = `
                <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            `;
            $('#alertContainer').html(alertDiv);

            // Auto hide after 5 seconds
            setTimeout(() => {
                $('.alert').fadeOut('slow', function() {
                    $(this).remove();
                });
            }, 5000);
        }

        // Confirm delete
        function confirmDelete(message = 'Apakah Anda yakin ingin menghapus data ini?') {
            return confirm(message);
        }

        // Format date to Indonesian format
        function formatDate(date) {
            const options = {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            };
            return new Date(date).toLocaleDateString('id-ID', options);
        }
    </script>
    <script>
        $(document).ready(function() {
            // Initialize DataTables
            $('.datatable').DataTable({
                language: {
                    url: '//cdn.datatables.net/plug-ins/1.13.7/i18n/id.json'
                }
            });
        });
    </script>
</body>
</html>
<?php
// End output buffering
ob_end_flush();
?>
