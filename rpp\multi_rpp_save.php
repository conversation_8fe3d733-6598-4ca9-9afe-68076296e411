<?php
require_once __DIR__ . '/../middleware/auth.php';
checkGuruAccess();
require_once '../config/database.php';
require_once '../models/Rpp.php';
require_once '../models/MultiRppExam.php';
require_once '../models/Guru.php';

// Cek jika bukan POST request
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    $_SESSION['error'] = "Akses tidak valid.";
    header("Location: multi_rpp_generate.php");
    exit();
}

// Validasi input
if (!isset($_POST['selected_rpps']) || !isset($_POST['selected_questions']) || !isset($_POST['questions'])) {
    $_SESSION['error'] = "Data tidak lengkap.";
    header("Location: multi_rpp_generate.php");
    exit();
}

$selected_rpp_ids = $_POST['selected_rpps'];
$selected_questions = $_POST['selected_questions'];
$questions_data = $_POST['questions'];
$exam_data = $_POST['exam_data'];
$config = $_POST['config'];

// Validasi jika ada soal yang dipilih
if (empty($selected_questions)) {
    $_SESSION['error'] = "Silakan pilih minimal satu soal untuk disimpan.";
    header("Location: multi_rpp_preview.php");
    exit();
}

// Dapatkan guru_id
$guru = new Guru();
$stmt = $guru->getByUserId($_SESSION['user_id']);
if ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
    $guru_id = $row['id'];
    $guru_data = $row;
} else {
    die("Data guru tidak ditemukan");
}

// Validasi RPP ownership
$rpp = new Rpp();
foreach ($selected_rpp_ids as $rpp_id) {
    $stmt = $rpp->getOne($rpp_id);
    $rpp_data = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$rpp_data || $rpp_data['guru_id'] != $guru_id) {
        $_SESSION['error'] = "RPP tidak ditemukan atau bukan milik Anda.";
        header("Location: multi_rpp_generate.php");
        exit();
    }
}

try {
    $database = new Database();
    $conn = $database->getConnection();
    $conn->beginTransaction();
    
    // 1. Simpan data ujian multi-RPP
    $multiRppExam = new MultiRppExam();
    $multiRppExam->guru_id = $guru_id;
    $multiRppExam->exam_title = $exam_data['exam_title'];
    $multiRppExam->exam_type = $exam_data['exam_type'];
    $multiRppExam->semester = $exam_data['semester'];
    $multiRppExam->tahun_ajaran = $exam_data['tahun_ajaran'];
    $multiRppExam->exam_duration = $exam_data['exam_duration'];
    $multiRppExam->total_score = $exam_data['total_score'];
    $multiRppExam->selected_rpp_ids = json_encode($selected_rpp_ids);
    $multiRppExam->question_distribution = json_encode($config);
    $multiRppExam->additional_notes = $exam_data['additional_notes'];
    
    $multi_exam_id = $multiRppExam->create();
    
    if (!$multi_exam_id) {
        throw new Exception("Gagal menyimpan data ujian multi-RPP.");
    }
    
    // 2. Siapkan data soal yang akan disimpan
    $questions_to_save = [];
    foreach ($selected_questions as $question_index) {
        if (isset($questions_data[$question_index])) {
            $question = $questions_data[$question_index];
            
            // Decode options if it's JSON string
            $options = $question['options'];
            if (is_string($options)) {
                $options = json_decode($options, true);
            }
            
            $questions_to_save[] = [
                'source_rpp_id' => $question['source_rpp_id'],
                'source_chapter' => $question['source_chapter'],
                'question_text' => $question['question_text'],
                'question_type' => $question['question_type'],
                'options' => $options,
                'correct_answer' => $question['correct_answer'],
                'difficulty_level' => $question['difficulty_level'],
                'cognitive_level' => $question['cognitive_level'],
                'category' => $question['category'],
                'source_type' => $question['source_type']
            ];
        }
    }
    
    // 3. Simpan soal-soal menggunakan method saveQuestions
    $saved_ids = $multiRppExam->saveQuestions($multi_exam_id, $questions_to_save);
    
    $conn->commit();
    
    $saved_count = count($saved_ids);
    $_SESSION['success'] = "Berhasil menyimpan ujian multi-RPP '{$exam_data['exam_title']}' dengan {$saved_count} soal.";
    
    // Redirect ke halaman detail ujian multi-RPP
    header("Location: multi_rpp_detail.php?exam_id=" . $multi_exam_id);
    exit();
    
} catch (Exception $e) {
    $conn->rollBack();
    $_SESSION['error'] = "Gagal menyimpan ujian multi-RPP: " . $e->getMessage();
    
    // Redirect kembali ke preview dengan data
    header("Location: multi_rpp_preview.php");
    exit();
}
?>
