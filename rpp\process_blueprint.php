<?php
// Handle AJAX request for blueprint generation FIRST
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'generate_blueprint') {
    // Start output buffering and set headers immediately
    ob_start();
    header('Content-Type: application/json');
    header('Cache-Control: no-cache, must-revalidate');

    try {
        // Check authentication within AJAX handler
        session_start();
        if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'guru') {
            ob_clean();
            echo json_encode([
                'success' => false,
                'error' => 'Unauthorized access'
            ], JSON_UNESCAPED_UNICODE);
            exit();
        }
        require_once '../config/database.php';
        require_once '../models/Rpp.php';
        require_once '../models/RppQuestion.php';
        require_once '../models/Guru.php';
        require_once '../models/GeminiApi.php';

        $rpp_id = $_POST['rpp_id'] ?? '';
        if (empty($rpp_id)) {
            throw new Exception("ID RPP tidak ditemukan.");
        }

        // Get guru_id
        $guru = new Guru();
        $stmt = $guru->getByUserId($_SESSION['user_id']);
        if ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $guru_id = $row['id'];
            $guru_data = $row;
        } else {
            throw new Exception("Data guru tidak ditemukan");
        }

        // Get RPP data
        $rpp = new Rpp();
        $stmt = $rpp->getOne($rpp_id);
        $rpp_data = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$rpp_data || $rpp_data['guru_id'] != $guru_id) {
            throw new Exception("RPP tidak ditemukan atau bukan milik Anda.");
        }

        // Get questions data
        $rppQuestion = new RppQuestion();
        $stmt = $rppQuestion->getByRppId($rpp_id);
        $questions = $stmt->fetchAll(PDO::FETCH_ASSOC);

        if (empty($questions)) {
            throw new Exception("Tidak ada soal yang tersedia untuk RPP ini.");
        }

        // Generate blueprint using AI
        $geminiApi = new GeminiApi();
        $config = [
            'exam_type' => $_POST['exam_type'] ?? 'Ujian Harian',
            'semester' => $_POST['semester'] ?? 'Ganjil',
            'exam_duration' => $_POST['exam_duration'] ?? '90',
            'total_score' => $_POST['total_score'] ?? '100',
            'additional_notes' => $_POST['additional_notes'] ?? '',
            'guru_data' => $guru_data,
            'blueprint_title' => $_POST['blueprint_title'] ?? $rpp_data['tema_subtema'] . ' - Kisi-kisi'
        ];

        $generated_blueprint = $geminiApi->generateBlueprint($rpp_data, $questions, $config);

        // Debug logging
        error_log("Single RPP Blueprint Generated - Data: " . json_encode($generated_blueprint));
        error_log("Single RPP Blueprint - Is Empty: " . (empty($generated_blueprint) ? 'YES' : 'NO'));
        error_log("Single RPP Blueprint - Is Null: " . (is_null($generated_blueprint) ? 'YES' : 'NO'));

        // Ensure clean output
        ob_clean();
        echo json_encode([
            'success' => true,
            'blueprint' => $generated_blueprint,
            'message' => 'Kisi-kisi berhasil di-generate',
            'debug_info' => [
                'blueprint_empty' => empty($generated_blueprint),
                'blueprint_null' => is_null($generated_blueprint),
                'blueprint_type' => gettype($generated_blueprint),
                'blueprint_keys' => is_array($generated_blueprint) ? array_keys($generated_blueprint) : 'not_array'
            ]
        ], JSON_UNESCAPED_UNICODE);

    } catch (Exception $e) {
        // Log the error for debugging
        error_log("Single RPP Blueprint Generation Error: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine());

        // Ensure clean output
        ob_clean();
        echo json_encode([
            'success' => false,
            'error' => $e->getMessage(),
            'debug_info' => [
                'file' => basename($e->getFile()),
                'line' => $e->getLine(),
                'rpp_id' => $_POST['rpp_id'] ?? 'not provided'
            ]
        ], JSON_UNESCAPED_UNICODE);
    } catch (Throwable $t) {
        // Catch any other errors including fatal errors
        error_log("Single RPP Blueprint Fatal Error: " . $t->getMessage() . " in " . $t->getFile() . " on line " . $t->getLine());

        ob_clean();
        echo json_encode([
            'success' => false,
            'error' => 'Terjadi kesalahan sistem yang tidak terduga',
            'debug_info' => [
                'file' => basename($t->getFile()),
                'line' => $t->getLine(),
                'type' => 'Fatal Error'
            ]
        ], JSON_UNESCAPED_UNICODE);
    }

    // Ensure we exit cleanly
    exit();
}

// Traditional form handling (non-AJAX)
require_once __DIR__ . '/../middleware/auth.php';
checkGuruAccess();
require_once '../config/database.php';
require_once '../models/Rpp.php';
require_once '../models/RppQuestion.php';
require_once '../models/GeminiApi.php';
require_once '../models/Guru.php';

// Check if request is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    $_SESSION['error'] = "Method not allowed";
    header("Location: generate_questions.php");
    exit();
}

// Validate input
$required_fields = ['rpp_id', 'exam_type', 'semester', 'exam_duration', 'total_score'];
foreach ($required_fields as $field) {
    if (!isset($_POST[$field]) || empty($_POST[$field])) {
        $_SESSION['error'] = "Field '$field' is required";
        header("Location: generate_blueprint.php?rpp_id=" . ($_POST['rpp_id'] ?? ''));
        exit();
    }
}

$rpp_id = $_POST['rpp_id'];
$exam_type = $_POST['exam_type'];
$semester = $_POST['semester'];
$exam_duration = intval($_POST['exam_duration']);
$total_score = intval($_POST['total_score']);
$additional_notes = $_POST['additional_notes'] ?? '';

try {
    // Get guru_id
    $guru = new Guru();
    $stmt = $guru->getByUserId($_SESSION['user_id']);
    if ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $guru_id = $row['id'];
        $guru_data = $row;
    } else {
        $_SESSION['error'] = "Data guru tidak ditemukan.";
        header("Location: generate_questions.php");
        exit();
    }

    // Get RPP data
    $rpp = new Rpp();
    $stmt = $rpp->getOne($rpp_id);
    $rpp_data = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$rpp_data || $rpp_data['guru_id'] != $guru_id) {
        $_SESSION['error'] = "RPP tidak ditemukan atau bukan milik Anda.";
        header("Location: generate_questions.php");
        exit();
    }

    // Get questions data
    $rppQuestion = new RppQuestion();
    $stmt = $rppQuestion->getByRppId($rpp_id);
    $questions = $stmt->fetchAll(PDO::FETCH_ASSOC);

    if (empty($questions)) {
        $_SESSION['error'] = "Tidak ada soal yang tersedia untuk RPP ini.";
        header("Location: generate_blueprint.php?rpp_id=" . $rpp_id);
        exit();
    }

    // Generate blueprint using AI
    $geminiApi = new GeminiApi();
    $blueprint_data = $geminiApi->generateBlueprint($rpp_data, $questions, [
        'exam_type' => $exam_type,
        'semester' => $semester,
        'exam_duration' => $exam_duration,
        'total_score' => $total_score,
        'additional_notes' => $additional_notes,
        'guru_data' => $guru_data
    ]);

    // Store blueprint data in session for export
    $_SESSION['blueprint_data'] = $blueprint_data;
    $_SESSION['blueprint_config'] = [
        'rpp_data' => $rpp_data,
        'questions' => $questions,
        'exam_type' => $exam_type,
        'semester' => $semester,
        'exam_duration' => $exam_duration,
        'total_score' => $total_score,
        'additional_notes' => $additional_notes,
        'guru_data' => $guru_data
    ];

    $_SESSION['success'] = "Kisi-kisi berhasil digenerate! Silakan pilih format export.";
    header("Location: blueprint_result.php?rpp_id=" . $rpp_id);
    exit();

} catch (Exception $e) {
    error_log("Blueprint Generation Error: " . $e->getMessage());
    $_SESSION['error'] = "Terjadi kesalahan saat menggenerate kisi-kisi: " . $e->getMessage();
    header("Location: generate_blueprint.php?rpp_id=" . $rpp_id);
    exit();
}
?>
